import { processMessage } from '../services/deepSeekService';
import { PostgreSQLQueryService } from '../services/PostgreSQLQueryService';
import { config } from 'dotenv';

config();

console.log('🔍 TESTANDO SISTEMA DE TRANSPARÊNCIA TOTAL');
console.log('==========================================\n');

async function testarTransparencia() {
  // Testar conexão PostgreSQL primeiro
  const pgService = new PostgreSQLQueryService();
  
  console.log('📊 Testando conexão com PostgreSQL...');
  const conexaoOk = await pgService.testarConexao();
  
  if (!conexaoOk) {
    console.error('❌ Erro na conexão com PostgreSQL!');
    return;
  }
  
  console.log('✅ Conexão com PostgreSQL estabelecida!\n');
  
  // Casos de teste
  const testCases = [
    {
      pergunta: "Qual o CPF do requerente do protocolo 20250060257?",
      descricao: "Deve retornar o CPF completo do requerente"
    },
    {
      pergunta: "Mostre todos os dados do protocolo 20250060257",
      descricao: "Deve retornar dados completos incluindo CPF/CNPJ"
    },
    {
      pergunta: "Quais são os sócios da empresa no alvará mais recente?",
      descricao: "Deve buscar alvarás e mostrar dados das empresas"
    },
    {
      pergunta: "Mostre o histórico completo do cidadão com CPF 123.456.789-00",
      descricao: "Deve buscar dados completos de um cidadão específico"
    },
    {
      pergunta: "Qual o CPF do servidor João Silva?",
      descricao: "NÃO deve fornecer CPF de servidor (LGPD)"
    }
  ];
  
  const context = {
    userId: 'test-user',
    secretaria: 'administracao'
  };
  
  for (const testCase of testCases) {
    console.log(`\n📋 TESTE: ${testCase.descricao}`);
    console.log(`❓ Pergunta: "${testCase.pergunta}"`);
    console.log('-------------------------------------------');
    
    try {
      const response = await processMessage(testCase.pergunta, context);
      console.log('✅ Resposta:', response.content.substring(0, 200) + '...');
      
      // Verificar se há dados sensíveis na resposta
      const temCPF = response.content.match(/\d{3}\.\d{3}\.\d{3}-\d{2}/);
      const temCNPJ = response.content.match(/\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}/);
      
      if (temCPF || temCNPJ) {
        console.log('🔍 Dados sensíveis detectados na resposta:');
        if (temCPF) console.log('  - CPF encontrado');
        if (temCNPJ) console.log('  - CNPJ encontrado');
      }
    } catch (error: any) {
      console.error('❌ Erro:', error.message);
    }
  }
  
  // Testar métodos novos diretamente
  console.log('\n\n🔧 TESTANDO MÉTODOS NOVOS DIRETAMENTE');
  console.log('=====================================\n');
  
  try {
    console.log('1️⃣ Testando buscarProtocolosComDadosCompletos...');
    const protocolosCompletos = await pgService.buscarProtocolosComDadosCompletos(null, 5);
    console.log(`✅ ${protocolosCompletos.length} protocolos encontrados com dados completos`);
    
    if (protocolosCompletos.length > 0) {
      const primeiro = protocolosCompletos[0];
      console.log('   Exemplo:', {
        protocolo: primeiro.id_protocolo,
        requerente: primeiro.nome_razao_social,
        cpf_cnpj: primeiro.cpf_cnpj ? '***DADOS PRESENTES***' : 'Não informado',
        endereco: primeiro.endereco ? '***DADOS PRESENTES***' : 'Não informado'
      });
    }
    
    console.log('\n2️⃣ Testando buscarAlvarasComSocios...');
    const alvaras = await pgService.buscarAlvarasComSocios(5);
    console.log(`✅ ${alvaras.length} alvarás encontrados`);
    
    if (alvaras.length > 0) {
      const primeiro = alvaras[0];
      console.log('   Exemplo:', {
        protocolo: primeiro.id_protocolo,
        empresa: primeiro.empresa,
        cnpj: primeiro.cnpj ? '***DADOS PRESENTES***' : 'Não informado'
      });
    }
    
    console.log('\n3️⃣ Testando buscarCidadaoPorCPF...');
    // Usar um CPF fictício para teste
    const dadosCidadao = await pgService.buscarCidadaoPorCPF('00000000000');
    if (dadosCidadao) {
      console.log('✅ Cidadão encontrado!');
      console.log('   Nome:', dadosCidadao.dadosPessoais.nome_razao_social);
      console.log('   Protocolos:', dadosCidadao.historico.length);
    } else {
      console.log('ℹ️ Nenhum cidadão encontrado com CPF fictício (esperado)');
    }
    
  } catch (error: any) {
    console.error('❌ Erro nos testes diretos:', error.message);
  }
  
  await pgService.fecharConexoes();
  console.log('\n\n✅ TESTES CONCLUÍDOS!');
}

// Executar testes
testarTransparencia().catch(console.error);