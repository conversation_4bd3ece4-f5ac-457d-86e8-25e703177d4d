# Script PowerShell para corrigir dependências no Windows
Write-Host "🔄 Corrigindo dependências para Windows..." -ForegroundColor Cyan

Write-Host "`n1. Limpando node_modules e lock files..." -ForegroundColor Yellow
if (Test-Path "node_modules") { Remove-Item -Recurse -Force "node_modules" }
if (Test-Path "backend/node_modules") { Remove-Item -Recurse -Force "backend/node_modules" }
if (Test-Path "frontend/node_modules") { Remove-Item -Recurse -Force "frontend/node_modules" }
if (Test-Path "package-lock.json") { Remove-Item -Force "package-lock.json" }
if (Test-Path "backend/package-lock.json") { Remove-Item -Force "backend/package-lock.json" }
if (Test-Path "frontend/package-lock.json") { Remove-Item -Force "frontend/package-lock.json" }

Write-Host "`n2. Instalando dependências root..." -ForegroundColor Yellow
npm install

Write-Host "`n3. Instalando dependências backend..." -ForegroundColor Yellow
Set-Location backend
npm install
Set-Location ..

Write-Host "`n4. Instalando dependências frontend..." -ForegroundColor Yellow
Set-Location frontend
npm install
Set-Location ..

Write-Host "`n✅ Instalações concluídas! Testando backend..." -ForegroundColor Green
Set-Location backend
Write-Host "`n🚀 Iniciando servidor de desenvolvimento..." -ForegroundColor Cyan
npm run dev