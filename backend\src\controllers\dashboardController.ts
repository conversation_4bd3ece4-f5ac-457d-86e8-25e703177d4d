import { Request, Response } from 'express';
import { cacheService } from '../services/cacheService';
import { queueService } from '../services/queueService';
import { isDiscountTime, getNextDiscountTime } from '../config/redis';

class DashboardController {
  // Dashboard completo de métricas
  static async getFullDashboard(req: Request, res: Response): Promise<void> {
    try {
      const [cacheMetrics, queueStats] = await Promise.all([
        cacheService.getCacheMetrics(),
        queueService.getQueueStats(),
      ]);

      const now = new Date();
      const nextDiscount = getNextDiscountTime();
      const timeToDiscount = Math.max(nextDiscount.getTime() - now.getTime(), 0);

      // Calcular economia real
      const totalMessages = cacheMetrics.totalRequests;
      const cacheHits = cacheMetrics.cacheHits;
      const discountMessages = queueStats.totalProcessed * (queueStats.discountUsageRate / 100);
      
      const normalCost = 0.00219; // Custo normal por mensagem
      const discountCost = 0.001095; // Custo com desconto
      
      const costWithoutOptimizations = totalMessages * normalCost;
      const actualCost = 
        (cacheHits * 0) + // Cache hits são gratuitos
        ((totalMessages - cacheHits) * normalCost) - // Mensagens normais
        (discountMessages * normalCost * 0.5); // Economia do desconto
      
      const totalSavings = costWithoutOptimizations - actualCost;
      const savingsPercentage = costWithoutOptimizations > 0 ? 
        (totalSavings / costWithoutOptimizations) * 100 : 0;

      const dashboard = {
        timestamp: now.toISOString(),
        summary: {
          totalMessages: totalMessages,
          cacheHitRate: cacheMetrics.hitRate,
          totalSavings: totalSavings,
          savingsPercentage: savingsPercentage,
          dailyCost: actualCost,
          monthlyCostEstimate: actualCost * 30,
        },
        
        cache: {
          metrics: cacheMetrics,
          performance: {
            hitRate: `${cacheMetrics.hitRate.toFixed(1)}%`,
            avgResponseTime: '< 50ms', // Cache é sempre rápido
            memoryUsage: 'N/A', // Seria obtido do Redis
          }
        },
        
        queue: {
          stats: queueStats,
          currentStatus: await queueService.getQueueStatus(),
          discountOptimization: {
            usageRate: `${queueStats.discountUsageRate.toFixed(1)}%`,
            totalSavings: queueStats.totalSavings,
            messagesInDiscount: Math.floor(queueStats.totalProcessed * (queueStats.discountUsageRate / 100)),
          }
        },
        
        discount: {
          isActive: isDiscountTime(),
          nextTime: nextDiscount.toISOString(),
          timeToNextMinutes: Math.floor(timeToDiscount / 60000),
          schedule: 'UTC 16:30-00:30 (8 horas diárias)',
          savingsRate: '50%',
        },
        
        costs: {
          current: {
            perMessage: isDiscountTime() ? '$0.001095' : '$0.00219',
            daily: `$${actualCost.toFixed(4)}`,
            monthly: `$${(actualCost * 30).toFixed(2)}`,
          },
          projections: {
            withoutCache: `$${costWithoutOptimizations.toFixed(2)}`,
            withOptimizations: `$${actualCost.toFixed(2)}`,
            monthlySavings: `$${(totalSavings * 30).toFixed(2)}`,
          },
          budget: {
            daily: '$50.00',
            monthly: '$1000.00',
            used: `${((actualCost / 50) * 100).toFixed(1)}%`,
            remaining: `$${(50 - actualCost).toFixed(2)}`,
          }
        },
        
        usage: {
          bySecretaria: await DashboardController.getUsageBySecretaria(),
          byUrgency: await DashboardController.getUsageByUrgency(),
          hourlyDistribution: await DashboardController.getHourlyDistribution(),
        },
        
        recommendations: DashboardController.generateRecommendations(
          cacheMetrics.hitRate, 
          queueStats.discountUsageRate,
          actualCost
        ),
      };

      res.json({
        success: true,
        data: dashboard,
      });

    } catch (error) {
      console.error('Erro ao gerar dashboard:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno ao gerar dashboard',
      });
    }
  }

  // Métricas simples para widget
  static async getSimpleMetrics(req: Request, res: Response): Promise<void> {
    try {
      const [cacheMetrics, queueStatus] = await Promise.all([
        cacheService.getCacheMetrics(),
        queueService.getQueueStatus(),
      ]);

      const metrics = {
        cacheHitRate: `${cacheMetrics.hitRate.toFixed(1)}%`,
        totalSavings: `$${cacheMetrics.totalSavings.toFixed(2)}`,
        messagesQueued: queueStatus.totalQueued,
        discountActive: isDiscountTime(),
        nextDiscount: getNextDiscountTime().toLocaleTimeString('pt-BR'),
      };

      res.json({
        success: true,
        data: metrics,
      });

    } catch (error) {
      console.error('Erro ao obter métricas simples:', error);
      res.status(500).json({
        success: false,
        error: 'Erro ao consultar métricas',
      });
    }
  }

  // Relatório de economia
  static async getSavingsReport(req: Request, res: Response): Promise<void> {
    try {
      const { period = 'today' } = req.query;
      
      const [cacheMetrics, queueStats] = await Promise.all([
        cacheService.getCacheMetrics(),
        queueService.getQueueStats(),
      ]);

      const baseCost = 0.00219;
      const totalMessages = cacheMetrics.totalRequests;
      
      const savings = {
        cache: {
          hits: cacheMetrics.cacheHits,
          savings: cacheMetrics.cacheHits * baseCost,
          percentage: cacheMetrics.hitRate,
        },
        discount: {
          messages: Math.floor(queueStats.totalProcessed * (queueStats.discountUsageRate / 100)),
          savings: queueStats.totalSavings,
          percentage: queueStats.discountUsageRate,
        },
        total: {
          messages: totalMessages,
          savings: cacheMetrics.totalSavings + queueStats.totalSavings,
          costWithoutOptimization: totalMessages * baseCost,
          actualCost: (totalMessages - cacheMetrics.cacheHits) * baseCost - queueStats.totalSavings,
        }
      };

      // Projeções
      const projections = {
        weekly: savings.total.savings * 7,
        monthly: savings.total.savings * 30,
        yearly: savings.total.savings * 365,
      };

      res.json({
        success: true,
        data: {
          period,
          savings,
          projections,
          recommendations: [
            savings.cache.percentage < 50 ? 'Considere ajustar TTL do cache para melhorar hit rate' : null,
            savings.discount.percentage < 30 ? 'Incentive usuários a usar horário de desconto' : null,
            savings.total.actualCost > 10 ? 'Custo diário alto - revisar limites por usuário' : null,
          ].filter(Boolean),
        }
      });

    } catch (error) {
      console.error('Erro ao gerar relatório de economia:', error);
      res.status(500).json({
        success: false,
        error: 'Erro ao gerar relatório',
      });
    }
  }

  // Uso por secretaria (dados reais do banco)
  private static async getUsageBySecretaria(): Promise<Record<string, number>> {
    try {
      // Consultar dados reais do banco de dados
      const query = `
        SELECT
          COALESCE(departamento, 'outros') as secretaria,
          COUNT(*) as total
        FROM conversas
        WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY departamento
        ORDER BY total DESC
        LIMIT 10
      `;

      const result = await pool.query(query);
      const usage: Record<string, number> = {};

      result.rows.forEach(row => {
        usage[row.secretaria] = parseInt(row.total);
      });

      return usage;
    } catch (error) {
      console.error('Erro ao buscar uso por secretaria:', error);
      return {};
    }
  }

  // Uso por urgência (dados reais)
  private static async getUsageByUrgency(): Promise<Record<string, number>> {
    try {
      const query = `
        SELECT
          CASE
            WHEN urgencia = 'alta' THEN 'immediate'
            WHEN urgencia = 'media' THEN 'normal'
            ELSE 'batch'
          END as urgency_level,
          COUNT(*) as total
        FROM conversas
        WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
        GROUP BY urgency_level
      `;

      const result = await pool.query(query);
      const usage: Record<string, number> = {};

      result.rows.forEach(row => {
        usage[row.urgency_level] = parseInt(row.total);
      });

      return usage;
    } catch (error) {
      console.error('Erro ao buscar uso por urgência:', error);
      return { immediate: 0, normal: 0, batch: 0 };
    }
  }

  // Distribuição por hora (dados reais)
  private static async getHourlyDistribution(): Promise<number[]> {
    try {
      const query = `
        SELECT
          EXTRACT(HOUR FROM created_at) as hour,
          COUNT(*) as total
        FROM conversas
        WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
        GROUP BY EXTRACT(HOUR FROM created_at)
        ORDER BY hour
      `;

      const result = await pool.query(query);
      const distribution = new Array(24).fill(0);

      result.rows.forEach(row => {
        const hour = parseInt(row.hour);
        distribution[hour] = parseInt(row.total);
      });

      return distribution;
    } catch (error) {
      console.error('Erro ao buscar distribuição por hora:', error);
      return new Array(24).fill(0);
    }
  }

  // Gerar recomendações baseadas nas métricas
  private static generateRecommendations(
    cacheHitRate: number, 
    discountUsageRate: number, 
    dailyCost: number
  ): string[] {
    const recommendations: string[] = [];

    if (cacheHitRate < 50) {
      recommendations.push(
        '🎯 Cache hit rate baixo. Considere ajustar TTL ou melhorar algoritmo de similaridade.'
      );
    } else if (cacheHitRate > 80) {
      recommendations.push(
        '✅ Excelente cache hit rate! Sistema otimizado para economia máxima.'
      );
    }

    if (discountUsageRate < 25) {
      recommendations.push(
        '💰 Poucas mensagens usando desconto. Eduque usuários sobre horários econômicos.'
      );
    }

    if (dailyCost > 10) {
      recommendations.push(
        '⚠️ Custo diário elevado. Revisar limites por usuário ou implementar mais cache.'
      );
    }

    if (isDiscountTime()) {
      recommendations.push(
        '🟢 Horário de desconto ativo! Melhor momento para processamento de lotes.'
      );
    } else {
      const nextDiscount = getNextDiscountTime();
      const hours = Math.floor((nextDiscount.getTime() - Date.now()) / 3600000);
      recommendations.push(
        `🟡 Próximo desconto em ${hours}h. Considere aguardar para relatórios não-urgentes.`
      );
    }

    if (recommendations.length === 0) {
      recommendations.push('🎉 Sistema funcionando otimamente! Todas as métricas dentro do esperado.');
    }

    return recommendations;
  }

  // Status em tempo real para monitoring
  static async getRealTimeStatus(req: Request, res: Response): Promise<void> {
    try {
      const queueStatus = await queueService.getQueueStatus();
      const cacheHealth = await cacheService.healthCheck();

      const status = {
        timestamp: new Date().toISOString(),
        system: {
          cache: cacheHealth ? 'healthy' : 'error',
          queue: 'healthy', // Baseado na conexão do Redis
          discount: isDiscountTime() ? 'active' : 'inactive',
        },
        queue: {
          totalMessages: queueStatus.totalQueued,
          processing: queueStatus.processingNow,
          estimatedWait: `${queueStatus.estimatedWaitTime.toFixed(0)} min`,
        },
        discount: {
          active: isDiscountTime(),
          nextTime: getNextDiscountTime(),
          savings: isDiscountTime() ? '50% ativo' : 'Próximo em ' + 
            Math.floor((getNextDiscountTime().getTime() - Date.now()) / 60000) + ' min',
        }
      };

      res.json({
        success: true,
        data: status,
      });

    } catch (error) {
      console.error('Erro ao obter status em tempo real:', error);
      res.status(500).json({
        success: false,
        error: 'Erro ao consultar status do sistema',
      });
    }
  }
}

export default DashboardController;