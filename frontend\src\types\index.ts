// Tipos de usuário e autenticação
export interface User {
  id: string;
  email: string;
  cpf?: string;
  nome: string;
  secretaria: SecretariaType;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthSession {
  userId: string;
  token: string;
  refreshToken: string;
  expiresAt: Date;
  secretaria: SecretariaType;
  role: UserRole;
}

// Tipos de chat e conversação
export interface ChatMessage {
  id: string;
  conversationId: string;
  userId: string;
  content: string;
  type: "user" | "assistant";
  timestamp: Date;
  metadata?: MessageMetadata;
}

export interface MessageMetadata {
  queryGenerated?: string;
  responseTime?: number;
  tokensUsed?: number;
  cost?: number;
}

export interface Conversation {
  id: string;
  userId: string;
  secretaria: SecretariaType;
  title: string;
  messages: ChatMessage[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Tipos de secretaria e permissões
export type SecretariaType =
  | "administracao"
  | "financas"
  | "saude"
  | "educacao"
  | "obras-urbanismo"
  | "assistencia-social"
  | "meio-ambiente";

export type UserRole = "admin" | "gestor" | "operador" | "consulta";

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Form types
export interface LoginFormData {
  email?: string;
  cpf?: string;
  password: string;
  secretaria: SecretariaType;
}

export interface ChatFormData {
  message: string;
  conversationId?: string;
}