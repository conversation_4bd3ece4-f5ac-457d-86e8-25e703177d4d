---
alwaysApply: true
---

# Regras Específicas de Implementação - Prefeitura Virtual IA

Este documento estabelece as diretrizes específicas e técnicas para o desenvolvimento do projeto **Prefeitura Virtual IA**, complementando as regras gerais. Estas diretrizes devem ser seguidas rigorosamente por todos os desenvolvedores.

### SEMPRE FALAR EM PT-BR ###

## 1. Padrões de Código por Tecnologia

### 1.1. TypeScript/JavaScript
- Utilizar TypeScript obrigatoriamente para todo o código frontend e backend
- Definir **interfaces** para todos os objetos de domínio e requests/responses
- Configuração do `tsconfig.json`:
  - `strict: true`
  - `noImplicitAny: true`
  - `strictNullChecks: true`
- Utilizar ES6+ (arrow functions, destructuring, template literals, etc.)
- Evitar `any` e utilizar tipos genéricos quando aplicável

### 1.2. React
- Componentes devem ser **funcionais** (React Hooks)
- Utilizar **TanStack Query** para todas as chamadas de API e estado de servidor
- Componentes pequenos e focados (máx. 300 linhas)
- Context API modular (por domínio) para estado global
- Custom hooks para lógica reutilizável
- Props obrigatoriamente tipadas

### 1.3. SQL/PostgreSQL
- Uso da biblioteca `pg` com **SQL parametrizado** (`$1`, `$2` …) – nunca concatenar strings
- RLS (Row Level Security) obrigatório para todas as tabelas sensíveis
- Views para consultas complexas frequentes
- Índices para campos de busca e JOIN frequentes
- Stored procedures para lógicas complexas compartilhadas
- Triggers para manter integridade referencial (incluindo `updated_at` automático)
- Normalização adequada (3NF na maioria dos casos)
- Convenção de nomenclatura:
  - Tabelas: singular, snake_case
  - Colunas: snake_case
  - PKs: sempre `id`
  - FKs: `{tabela_referenciada}_id`

### 1.4. Python (Microsserviços IA)
- Seguir PEP 8
- Type hints obrigatórios
- Docstrings para todas as funções (formato NumPy/Google)
- Testes unitários com pytest
- Configuração de ambientes com Poetry
- FastAPI para APIs

## 2. Padrões de Interação e API

### 2.1. REST API
- Endpoints RESTful e recursos nomeados em substantivos
- Verbos HTTP adequados (GET, POST, PUT, PATCH, DELETE)
- Respostas JSON consistentes, incluindo metadados quando aplicável
- Paginação por offset+limit ou cursor para listas
- Códigos HTTP apropriados (200, 201, 400, 401, 403, 404, 500)
- Erros com mensagens significativas e códigos de erro internos
- Versão em header ou path para APIs públicas

### 2.2. Autenticação e Autorização
- **Fluxo baseado em link mágico**: o usuário recebe um link contendo **token opaco permanente** (UUID).
- Token enviado em todas as requisições no header `Authorization: Bearer <token>`
- Tokens armazenados na tabela `access_tokens (token, user_id, created_at, revoked_at)`
- Revogação via endpoint `/api/auth/revoke-token/:tokenId`
- Rate limiting por token (30 mensagens/min)
- Permissões granulares armazenadas no perfil do usuário
- Verificação de permissões no frontend e backend (dupla camada)
- CORS configurado apenas para domínios conhecidos

### 2.3. WebSockets e Realtime
- Utilizar **WebSockets (ws ou Socket.io)** para funcionalidades em tempo real
- Implementar fallback para polling em caso de falha
- Manter conexões eficientes (não abrir múltiplas conexões)
- Evitar broadcasts desnecessários

## 3. Segurança e Performance

### 3.1. Segurança
- OWASP Top 10 como referência mínima
- Sanitização de inputs em todas as camadas (Zod no backend, React Hook Form/Zod no frontend)
- Proteção contra XSS, CSRF e SQL Injection
- Content Security Policy implementada
- Secrets nunca em código fonte ou logs
- Auditoria de ações sensíveis (token + IP + user-agent)
- Validação de dados em ambos frontend e backend

### 3.2. Performance
- Bundle splitting para otimização de carregamento inicial
- Lazy loading para componentes pesados e rotas
- Otimização de imagens (WebP, compressão, dimensões adequadas)
- Memoization para computações caras (`useMemo`, `useCallback`)
- Virtualização para listas longas
- Paginação para grandes conjuntos de dados
- Caching estratégico (TanStack Query + Redis Exact/Semantic/Context)
- Monitoramento de performance (web vitals)

### 3.3. Acessibilidade
- WCAG 2.1 AA como meta
- Uso consistente de landmarks, headings e aria-labels
- Contraste adequado para texto
- Suporte a navegação por teclado
- Textos alternativos para imagens
- Testes com leitores de tela

## 4. Estratégias de Escalabilidade

### 4.1. Horizontal Scaling
- Statelessness para permitir múltiplas instâncias
- Backend preparado para distribuição de carga
- Microsserviços isolados para funções intensivas
- Edge services leves (ex.: fila Bull + workers)
- Estratégia de caching em múltiplas camadas

### 4.2. Particionamento de Dados
- Estratégia clara para crescimento de dados (particionamento)
- Índices cuidadosamente planejados (consultar `EXPLAIN`)
- VACUUM/ANALYZE regulares

### 4.3. Modularização
- Código organizado por domínios de negócio
- Limites claros entre módulos
- Interfaces bem definidas para comunicação inter-módulos
- DDD (Domain-Driven Design) para módulos complexos

## 5. Fluxo de Desenvolvimento Ágil

*(mantido igual à versão anterior)*

## 6. Estrutura Específica para Módulos

*(mantida igual — atualize caso o nome dos módulos mude)*

## 7. Integração com IA

- Sem mudanças: continuar com pgvector, embeddings, RAG, etc.

## 8. Especificidades para Fases do Projeto

*(mantido igual)*

## 9. Padrões Específicos Implementados

### 9.1. Sistema de Autenticação
1. Acesso via **link mágico** contendo token opaco (UUID v4) gerado em `/api/auth/create-token`.
2. Validação do token na tabela `access_tokens`; tokens podem ser revogados a qualquer momento.
3. Isolamento de dados: todas as consultas SQL devem usar `WHERE user_id = $1` para evitar vazamento de conversas.
4. Rate limiting por token.
5. Middleware `authenticateToken` obrigatório em todas as rotas `/api/*`, exceto `/health`.

### 9.2. Estrutura de Permissões
```typescript
interface Permissao {
  modulo: ModuloSistema;
  acao: AcaoPermissao;
  nivel: NivelAcesso;
}

<ProtectedComponent
  modulo={ModuloSistema.CONVERSAS}
  acao={AcaoPermissao.VER}
  fallback={<Navigate to="/" replace />}
>
  {/* Conteúdo protegido */}
</ProtectedComponent>
```

### 9.3. Padrão de Rotas
- **Rotas públicas:** `/` (landing) e quaisquer páginas informativas
- **Rotas protegidas:** `/chat`, `/admin/*`
- **Proteção obrigatória:** usar `PrivateRoute`/`ProtectedComponent`
- **Redirecionamento:** baseado no perfil do usuário (se aplicável)

### 9.4. Estrutura de Componentes
*(mantida igual — ver seções anteriores)*

### 9.5. Padrão de Banco de Dados
- PostgreSQL gerenciado manualmente (sem Supabase MCP)
- Conexão via pool `pg` reutilizável
- RLS obrigatório onde apropriado
- Triggers automáticos (`updated_at`, histórico, etc.)
- Nomenclatura snake_case para tabelas e colunas
- Relacionamentos com `ON DELETE CASCADE` ou `RESTRICT` conforme necessidade

## 10. Checklist de Qualidade

### 10.1. Antes de Commit
- [ ] Código TypeScript sem erros de `tsc`
- [ ] Componentes tipados corretamente
- [ ] RLS implementado para novas tabelas sensíveis
- [ ] Token obrigatório validado nas novas rotas
- [ ] Responsividade testada
- [ ] Acessibilidade básica verificada

### 10.2. Antes de Deploy
- [ ] Build sem erros
- [ ] Testes funcionais básicos
- [ ] Migrações SQL testadas
- [ ] Variáveis de ambiente configuradas
- [ ] Performance básica verificada

### 10.3. Code Review
- [ ] Padrões de código seguidos
- [ ] Segurança verificada (principalmente uso correto do token e SQL parametrizado)
- [ ] Performance considerada
- [ ] Documentação atualizada
- [ ] Testes adequados

---

*Última atualização: 2025-07-30*
*Versão: 3.0.0*# Regras Específicas de Implementação - Prefeitura Virtual IA

Este documento estabelece as diretrizes específicas e técnicas para o desenvolvimento do projeto **Prefeitura Virtual IA**, complementando as regras gerais. Estas diretrizes devem ser seguidas rigorosamente por todos os desenvolvedores.

### SEMPRE FALAR EM PT-BR ###

## 1. Padrões de Código por Tecnologia

### 1.1. TypeScript/JavaScript
- Utilizar TypeScript obrigatoriamente para todo o código frontend e backend
- Definir **interfaces** para todos os objetos de domínio e requests/responses
- Configuração do `tsconfig.json`:
  - `strict: true`
  - `noImplicitAny: true`
  - `strictNullChecks: true`
- Utilizar ES6+ (arrow functions, destructuring, template literals, etc.)
- Evitar `any` e utilizar tipos genéricos quando aplicável

### 1.2. React
- Componentes devem ser **funcionais** (React Hooks)
- Utilizar **TanStack Query** para todas as chamadas de API e estado de servidor
- Componentes pequenos e focados (máx. 300 linhas)
- Context API modular (por domínio) para estado global
- Custom hooks para lógica reutilizável
- Props obrigatoriamente tipadas

### 1.3. SQL/PostgreSQL
- Uso da biblioteca `pg` com **SQL parametrizado** (`$1`, `$2` …) – nunca concatenar strings
- RLS (Row Level Security) obrigatório para todas as tabelas sensíveis
- Views para consultas complexas frequentes
- Índices para campos de busca e JOIN frequentes
- Stored procedures para lógicas complexas compartilhadas
- Triggers para manter integridade referencial (incluindo `updated_at` automático)
- Normalização adequada (3NF na maioria dos casos)
- Convenção de nomenclatura:
  - Tabelas: singular, snake_case
  - Colunas: snake_case
  - PKs: sempre `id`
  - FKs: `{tabela_referenciada}_id`

### 1.4. Python (Microsserviços IA)
- Seguir PEP 8
- Type hints obrigatórios
- Docstrings para todas as funções (formato NumPy/Google)
- Testes unitários com pytest
- Configuração de ambientes com Poetry
- FastAPI para APIs

## 2. Padrões de Interação e API

### 2.1. REST API
- Endpoints RESTful e recursos nomeados em substantivos
- Verbos HTTP adequados (GET, POST, PUT, PATCH, DELETE)
- Respostas JSON consistentes, incluindo metadados quando aplicável
- Paginação por offset+limit ou cursor para listas
- Códigos HTTP apropriados (200, 201, 400, 401, 403, 404, 500)
- Erros com mensagens significativas e códigos de erro internos
- Versão em header ou path para APIs públicas

### 2.2. Autenticação e Autorização
- **Fluxo baseado em link mágico**: o usuário recebe um link contendo **token opaco permanente** (UUID).
- Token enviado em todas as requisições no header `Authorization: Bearer <token>`
- Tokens armazenados na tabela `access_tokens (token, user_id, created_at, revoked_at)`
- Revogação via endpoint `/api/auth/revoke-token/:tokenId`
- Rate limiting por token (30 mensagens/min)
- Permissões granulares armazenadas no perfil do usuário
- Verificação de permissões no frontend e backend (dupla camada)
- CORS configurado apenas para domínios conhecidos

### 2.3. WebSockets e Realtime
- Utilizar **WebSockets (ws ou Socket.io)** para funcionalidades em tempo real
- Implementar fallback para polling em caso de falha
- Manter conexões eficientes (não abrir múltiplas conexões)
- Evitar broadcasts desnecessários

## 3. Segurança e Performance

### 3.1. Segurança
- OWASP Top 10 como referência mínima
- Sanitização de inputs em todas as camadas (Zod no backend, React Hook Form/Zod no frontend)
- Proteção contra XSS, CSRF e SQL Injection
- Content Security Policy implementada
- Secrets nunca em código fonte ou logs
- Auditoria de ações sensíveis (token + IP + user-agent)
- Validação de dados em ambos frontend e backend

### 3.2. Performance
- Bundle splitting para otimização de carregamento inicial
- Lazy loading para componentes pesados e rotas
- Otimização de imagens (WebP, compressão, dimensões adequadas)
- Memoization para computações caras (`useMemo`, `useCallback`)
- Virtualização para listas longas
- Paginação para grandes conjuntos de dados
- Caching estratégico (TanStack Query + Redis Exact/Semantic/Context)
- Monitoramento de performance (web vitals)

### 3.3. Acessibilidade
- WCAG 2.1 AA como meta
- Uso consistente de landmarks, headings e aria-labels
- Contraste adequado para texto
- Suporte a navegação por teclado
- Textos alternativos para imagens
- Testes com leitores de tela

## 4. Estratégias de Escalabilidade

### 4.1. Horizontal Scaling
- Statelessness para permitir múltiplas instâncias
- Backend preparado para distribuição de carga
- Microsserviços isolados para funções intensivas
- Edge services leves (ex.: fila Bull + workers)
- Estratégia de caching em múltiplas camadas

### 4.2. Particionamento de Dados
- Estratégia clara para crescimento de dados (particionamento)
- Índices cuidadosamente planejados (consultar `EXPLAIN`)
- VACUUM/ANALYZE regulares

### 4.3. Modularização
- Código organizado por domínios de negócio
- Limites claros entre módulos
- Interfaces bem definidas para comunicação inter-módulos
- DDD (Domain-Driven Design) para módulos complexos

## 5. Fluxo de Desenvolvimento Ágil

*(mantido igual à versão anterior)*

## 6. Estrutura Específica para Módulos

*(mantida igual — atualize caso o nome dos módulos mude)*

## 7. Integração com IA

- Sem mudanças: continuar com pgvector, embeddings, RAG, etc.

## 8. Especificidades para Fases do Projeto

*(mantido igual)*

## 9. Padrões Específicos Implementados

### 9.1. Sistema de Autenticação
1. Acesso via **link mágico** contendo token opaco (UUID v4) gerado em `/api/auth/create-token`.
2. Validação do token na tabela `access_tokens`; tokens podem ser revogados a qualquer momento.
3. Isolamento de dados: todas as consultas SQL devem usar `WHERE user_id = $1` para evitar vazamento de conversas.
4. Rate limiting por token.
5. Middleware `authenticateToken` obrigatório em todas as rotas `/api/*`, exceto `/health`.

### 9.2. Estrutura de Permissões
```typescript
interface Permissao {
  modulo: ModuloSistema;
  acao: AcaoPermissao;
  nivel: NivelAcesso;
}

<ProtectedComponent
  modulo={ModuloSistema.CONVERSAS}
  acao={AcaoPermissao.VER}
  fallback={<Navigate to="/" replace />}
>
  {/* Conteúdo protegido */}
</ProtectedComponent>
```

### 9.3. Padrão de Rotas
- **Rotas públicas:** `/` (landing) e quaisquer páginas informativas
- **Rotas protegidas:** `/chat`, `/admin/*`
- **Proteção obrigatória:** usar `PrivateRoute`/`ProtectedComponent`
- **Redirecionamento:** baseado no perfil do usuário (se aplicável)

### 9.4. Estrutura de Componentes
*(mantida igual — ver seções anteriores)*

### 9.5. Padrão de Banco de Dados
- PostgreSQL gerenciado manualmente (sem Supabase MCP)
- Conexão via pool `pg` reutilizável
- RLS obrigatório onde apropriado
- Triggers automáticos (`updated_at`, histórico, etc.)
- Nomenclatura snake_case para tabelas e colunas
- Relacionamentos com `ON DELETE CASCADE` ou `RESTRICT` conforme necessidade

## 10. Checklist de Qualidade

### 10.1. Antes de Commit
- [ ] Código TypeScript sem erros de `tsc`
- [ ] Componentes tipados corretamente
- [ ] RLS implementado para novas tabelas sensíveis
- [ ] Token obrigatório validado nas novas rotas
- [ ] Responsividade testada
- [ ] Acessibilidade básica verificada

### 10.2. Antes de Deploy
- [ ] Build sem erros
- [ ] Testes funcionais básicos
- [ ] Migrações SQL testadas
- [ ] Variáveis de ambiente configuradas
- [ ] Performance básica verificada

### 10.3. Code Review
- [ ] Padrões de código seguidos
- [ ] Segurança verificada (principalmente uso correto do token e SQL parametrizado)
- [ ] Performance considerada
- [ ] Documentação atualizada
- [ ] Testes adequados

---

*Última atualização: 2025-07-30*
*Versão: 3.0.0*