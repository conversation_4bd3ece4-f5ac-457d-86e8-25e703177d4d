/**
 * Rotas para gerenciamento de tokens de acesso
 */

import { Router } from 'express';
import { accessTokenService } from '../services/accessTokenService';
import { postgresRepository } from '../repositories/PostgreSQLRepository';
import { z } from 'zod';

const router = Router();

// Schema de validação
const validateTokenSchema = z.object({
  token: z.string().min(10)
});

/**
 * @route POST /api/auth/validate-token
 * @desc Validar token de acesso
 * @access Public
 */
router.post('/validate-token', async (req, res) => {
  try {
    const validatedData = validateTokenSchema.parse(req.body);
    const token = req.headers['x-access-token'] as string || validatedData.token;

    const result = await accessTokenService.verifyTokenIntegrity(token);

    if (result.valid) {
      res.json({
        success: true,
        user: result.user,
        message: 'Token válido'
      });
    } else {
      res.status(401).json({
        success: false,
        error: result.error || 'Token inválido'
      });
    }

  } catch (error: any) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Dados inválidos',
        details: error.errors
      });
    }

    console.error('Erro ao validar token:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno no servidor'
    });
  }
});

/**
 * @route POST /api/admin/create-token
 * @desc Criar novo token de acesso (apenas para admin)
 * @access Admin
 */
router.post('/create-token', async (req, res) => {
  try {
    // Validação básica - em produção, implementar autenticação de admin
    const adminKey = req.headers['x-admin-key'];
    if (adminKey !== process.env.ADMIN_SECRET_KEY) {
      return res.status(403).json({
        success: false,
        error: 'Acesso negado'
      });
    }

    const options = {
      description: req.body.description || undefined,
      customToken: req.body.customToken || undefined
    };

    const tokenData = await accessTokenService.createAccessToken(options);
    const accessLink = accessTokenService.generateAccessLink(tokenData.token);

    res.status(201).json({
      success: true,
      data: {
        token: tokenData.token,
        userId: tokenData.userId,
        description: tokenData.description,
        accessLink,
        createdAt: tokenData.createdAt
      }
    });

  } catch (error: any) {
    console.error('Erro ao criar token:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Erro ao criar token'
    });
  }
});

/**
 * @route GET /api/admin/tokens
 * @desc Listar tokens ativos (apenas para admin)
 * @access Admin
 */
router.get('/tokens', async (req, res) => {
  try {
    const adminKey = req.headers['x-admin-key'];
    if (adminKey !== process.env.ADMIN_SECRET_KEY) {
      return res.status(403).json({
        success: false,
        error: 'Acesso negado'
      });
    }

    const tokens = await accessTokenService.listActiveTokens();

    res.json({
      success: true,
      data: tokens.map(token => ({
        token: token.token.substring(0, 8) + '...' + token.token.substring(token.token.length - 4), // Mascarar token
        userId: token.userId,
        description: token.description,
        isActive: token.isActive,
        createdAt: token.createdAt,
        accessLink: accessTokenService.generateAccessLink(token.token)
      }))
    });

  } catch (error: any) {
    console.error('Erro ao listar tokens:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao listar tokens'
    });
  }
});

/**
 * @route DELETE /api/admin/tokens/:token
 * @desc Revogar token de acesso (apenas para admin)
 * @access Admin
 */
router.delete('/tokens/:token', async (req, res) => {
  try {
    const adminKey = req.headers['x-admin-key'];
    if (adminKey !== process.env.ADMIN_SECRET_KEY) {
      return res.status(403).json({
        success: false,
        error: 'Acesso negado'
      });
    }

    const token = req.params.token;
    const revoked = await accessTokenService.revokeAccessToken(token);

    if (revoked) {
      res.json({
        success: true,
        message: 'Token revogado com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Token não encontrado'
      });
    }

  } catch (error: any) {
    console.error('Erro ao revogar token:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao revogar token'
    });
  }
});

/**
 * @route POST /api/admin/ensure-db-structure
 * @desc Garantir que as colunas necessárias existam no banco
 * @access Admin
 */
router.post('/ensure-db-structure', async (req, res) => {
  try {
    const adminKey = req.headers['x-admin-key'];
    if (adminKey !== process.env.ADMIN_SECRET_KEY) {
      return res.status(403).json({
        success: false,
        error: 'Acesso negado'
      });
    }

    await postgresRepository.ensureAnonymousUserColumns();

    res.json({
      success: true,
      message: 'Estrutura do banco atualizada'
    });

  } catch (error: any) {
    console.error('Erro ao atualizar estrutura:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao atualizar estrutura do banco'
    });
  }
});

export default router;