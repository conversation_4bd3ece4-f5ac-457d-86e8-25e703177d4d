{"name": "chatbot-frontend", "version": "1.0.0", "description": "Frontend para o Sistema de Chatbot Inteligente", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query": "^5.14.2", "autoprefixer": "^10.4.16", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^11.18.2", "lucide-react": "^0.294.0", "next": "^14.0.4", "next-themes": "^0.4.6", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.4", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.3.6", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/jest": "^29.5.8", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}