# Documentação do Sistema de Chatbot Inteligente

## Índice da Documentação

### 📁 Documentação Técnica

- **[Redis Architecture](./redis-architecture.md)** - Sistema completo de cache multi-camada e otimização de custos
- **[Database Architecture](./database-architecture.md)** - Arquitetura dos bancos PostgreSQL, MongoDB e Redis
- **[API Documentation](./api-documentation.md)** - Endpoints, schemas e exemplos de uso
- **[Security Guide](./security-guide.md)** - Segurança, autenticação e rate limiting

### 🚀 Guias de Instalação

- **[Local Development Setup](./local-setup.md)** - Configuração do ambiente de desenvolvimento
- **[Redis Installation Guide](./redis-installation.md)** - Instalação e configuração do Redis
- **[Database Setup](./database-setup.md)** - Configuração dos bancos de dados

### 📊 Monitoramento e Performance

- **[Performance Metrics](./performance-metrics.md)** - Métricas de cache, custos e performance
- **[Logging and Monitoring](./logging-monitoring.md)** - Sistema de logs e monitoramento
- **[Troubleshooting](./troubleshooting.md)** - Resolução de problemas comuns

### 🏛️ Funcionalidades do Sistema

- **[Chat System](./chat-system.md)** - Sistema de chat inteligente por secretaria
- **[Queue Management](./queue-management.md)** - Sistema de filas e processamento
- **[Cost Optimization](./cost-optimization.md)** - Otimização de custos com IA

## Estrutura do Projeto

```
pv_kiro/
├── backend/           # API Node.js + Express + TypeScript
├── frontend/          # Next.js 14 + React + Tailwind
├── docs/             # Documentação técnica (este diretório)
└── CLAUDE.md         # Guia principal para desenvolvimento
```

## Links Rápidos

- 🏠 **[CLAUDE.md](../CLAUDE.md)** - Guia principal de desenvolvimento
- 📋 **[README.md](../README.md)** - Visão geral do projeto
- ⚙️ **[Backend README](../backend/README.md)** - Documentação específica do backend
- 🖥️ **[Frontend README](../frontend/README.md)** - Documentação específica do frontend

## Comandos Rápidos

```bash
# Desenvolvimento completo
npm run dev

# Testes
npm run test

# Build de produção
npm run build

# Documentação Redis
cd docs && cat redis-architecture.md
```

---

**Sistema de Chatbot Inteligente para Secretarias da Prefeitura de Valparaíso de Goiás**