PS D:\PROJETOS-BACKUP\prefeitura_virtual_ia> cd backend
PS D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend> npm run dev

> chatbot-backend@1.0.0 dev
> tsx watch src/index.ts

✅ Redis Cache conectado
✅ Redis Queue conectado
✅ Redis connections initialized
🚀 Servidor rodando na porta 3001
📊 Health check: http://localhost:3001/health
🎯 Chat API: http://localhost:3001/api/chat/message
💰 Sistema de cache e desconto ativo!
⚡ Economia esperada: 60-70% com cache + horários de desconto
🚀 Processamento IMEDIATO para administracao
🤖 Processando mensagem para administracao: Pode me informar em qual departamente esta lotado ...
🤖 [TS] Processando mensagem para administracao - VERSÃO TYPESCRIPT
🔍 [TS] Arquivo: deepSeekService.ts sendo executado
🔍 [DEBUG] Mensagem original: Pode me informar em qual departamente esta lotado a funcionaria Rudilene?
🔍 [DEBUG] Mensagem lowercase: pode me informar em qual departamente esta lotado a funcionaria rudilene?
🔍 Buscando dados completos para transparência total...
⚠️ CONTEXTO IMPLÍCITO DETECTADO - Limitando busca para evitar confusão
✅ Dados carregados: 3 protocolos, 3 alvarás
🔍 Buscando documentos relevantes via RAG...
🔍 Busca híbrida: "Pode me informar em qual departamente esta lotado a funcionaria Rudilene?" | Secretaria: administracao
🔄 Inicializando Simple RAG Service...
✅ Vector store carregado: 276 documentos
✅ Simple RAG Service inicializado com sucesso
🔍 Buscando: "Pode me informar em qual departamente esta lotado a funcionaria Rudilene?" (top-6, threshold: 0.5)
✅ Encontrados 6 documentos relevantes
✅ Busca híbrida: 3 documentos finais
✅ RAG: 3 documentos encontrados em 3569ms
🔍 Debug tokens: {
  raw_usage: {
    prompt_tokens: 1909,
    completion_tokens: 161,
    total_tokens: 2070,
    prompt_tokens_details: { cached_tokens: 320 },
    prompt_cache_hit_tokens: 320,
    prompt_cache_miss_tokens: 1589
  },
  calculated: { prompt: 1909, completion: 161, total: 2070 }
}
💰 Debug custos: {
  isDiscount: true,
  tokens: { prompt: 1909, completion: 161, total: 2070 },
  costs: { promptCost: 0.000257715, completionCost: 0.00008855000000000001 },
  totalCost: 0.000346265,
  withDiscount: 0.000346265,
  savings: 0.000346265
}
✅ Resposta gerada em 13001ms
📊 Tokens: 2070 | Custo: $0.0003 | Com desconto: $0.0003
💰 Custo registrado: $0.0003 | Tokens: 2070 | Cache: MISS
❌ [PostgreSQL] userId inválido: "user" - deve ser um número
Erro ao garantir conversa: Error: Usuário não encontrado
    at Function.ensureConversation (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\conversationController.ts:49:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processMessage (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\chatController.ts:78:34)
Erro ao criar/buscar conversa: Error: Usuário não encontrado
    at Function.ensureConversation (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\conversationController.ts:49:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processMessage (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\chatController.ts:78:34)
📊 Rate limit atualizado - User: user, Messages: +1, Tokens: +500
