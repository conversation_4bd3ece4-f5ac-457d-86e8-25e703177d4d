import { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaFileAlt, FaCalendarAlt, FaTag, FaEye } from 'react-icons/fa';

interface PublicProtocolInfo {
  numeroProtocolo: string;
  dataProtocolo: string;
  situacao: string;
  assunto: string;
  ultimaAtualizacao: string;
}

interface ProtocolSearchProps {
  onSearch: (numero: string) => Promise<PublicProtocolInfo | null>;
}

export function ProtocolSearch({ onSearch }: ProtocolSearchProps) {
  const [numeroProtocolo, setNumeroProtocolo] = useState('');
  const [loading, setLoading] = useState(false);
  const [resultado, setResultado] = useState<PublicProtocolInfo | null>(null);
  const [erro, setErro] = useState<string | null>(null);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!numeroProtocolo.trim()) {
      setErro('Digite o número do protocolo');
      return;
    }

    setLoading(true);
    setErro(null);
    setResultado(null);

    try {
      const result = await onSearch(numeroProtocolo.trim());
      
      if (result) {
        setResultado(result);
      } else {
        setErro('Protocolo não encontrado');
      }
    } catch (error: any) {
      setErro(error.message || 'Erro ao consultar protocolo');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8"
    >
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-white mb-2">
          Consultar Protocolo
        </h3>
        <p className="text-gray-400">
          Digite o número do protocolo para consultar o status público
        </p>
      </div>

      <form onSubmit={handleSearch} className="space-y-4">
        <div className="relative">
          <input
            type="text"
            value={numeroProtocolo}
            onChange={(e) => setNumeroProtocolo(e.target.value)}
            placeholder="Ex: 2025000001"
            className="w-full px-4 py-3 pl-12 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
          />
          <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>

        <motion.button
          type="submit"
          disabled={loading}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl text-white font-semibold transition-all hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <span className="flex items-center justify-center gap-2">
              <FaSpinner className="animate-spin" />
              Consultando...
            </span>
          ) : (
            <span className="flex items-center justify-center gap-2">
              <FaSearch />
              Consultar Protocolo
            </span>
          )}
        </motion.button>
      </form>

      {/* Resultado da consulta */}
      {resultado && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6 p-6 bg-green-500/10 border border-green-500/30 rounded-xl"
        >
          <div className="flex items-center gap-2 mb-4">
            <FaFileAlt className="text-green-400" />
            <h4 className="text-lg font-semibold text-white">
              Protocolo Encontrado
            </h4>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-3">
              <div>
                <p className="text-gray-400 mb-1">Número do Protocolo</p>
                <p className="text-white font-medium">{resultado.numeroProtocolo}</p>
              </div>
              
              <div>
                <p className="text-gray-400 mb-1 flex items-center gap-1">
                  <FaCalendarAlt className="text-xs" />
                  Data do Protocolo
                </p>
                <p className="text-white">{formatDate(resultado.dataProtocolo)}</p>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <p className="text-gray-400 mb-1 flex items-center gap-1">
                  <FaEye className="text-xs" />
                  Situação Atual
                </p>
                <span className="inline-block px-3 py-1 bg-blue-500/20 border border-blue-500/30 rounded-full text-blue-300 text-xs font-medium">
                  {resultado.situacao}
                </span>
              </div>
              
              <div>
                <p className="text-gray-400 mb-1 flex items-center gap-1">
                  <FaTag className="text-xs" />
                  Assunto
                </p>
                <p className="text-white">{resultado.assunto}</p>
              </div>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t border-green-500/20">
            <p className="text-xs text-gray-400">
              Última atualização: {formatDate(resultado.ultimaAtualizacao)}
            </p>
          </div>
        </motion.div>
      )}

      {/* Erro */}
      {erro && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-4 p-4 bg-red-500/10 border border-red-500/30 rounded-xl"
        >
          <p className="text-red-400 text-sm flex items-center gap-2">
            <FaFileAlt />
            {erro}
          </p>
        </motion.div>
      )}
    </motion.div>
  );
}