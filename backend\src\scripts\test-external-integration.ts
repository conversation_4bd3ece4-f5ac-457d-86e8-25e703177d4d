/**
 * Script de teste completo para integração externa
 * Testa todo o fluxo de integração com sistema da prefeitura
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3001';
const SYSTEM_KEY = 'prefeitura_valparaiso_2024_integration_key_secure';
const ADMIN_KEY = 'admin_pv_valparaiso_2024_secure_key';

interface TestUser {
  userId: string;
  name: string;
  email: string;
  secretaria: string;
  cargo: string;
}

const testUsers: TestUser[] = [
  {
    userId: '123',
    name: '<PERSON>',
    email: '<EMAIL>',
    secretaria: 'Saúde',
    cargo: 'Enfermei<PERSON>'
  },
  {
    userId: '456',
    name: '<PERSON>',
    email: '<EMAIL>',
    secretaria: 'Educação',
    cargo: '<PERSON><PERSON>'
  },
  {
    userId: '789',
    name: '<PERSON>',
    email: '<EMAIL>',
    secretaria: 'Administração',
    cargo: 'Analista'
  }
];

async function testHealthEndpoint() {
  console.log('\n🔍 1. Testando endpoint de saúde...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/external/health`);
    const data = await response.json();
    
    if (data.success && data.status === 'healthy') {
      console.log('✅ Endpoint de saúde OK');
      return true;
    } else {
      console.log('❌ Endpoint de saúde falhou:', data);
      return false;
    }
  } catch (error) {
    console.log('❌ Erro ao testar endpoint de saúde:', error);
    return false;
  }
}

async function testFirstAccess(user: TestUser) {
  console.log(`\n🆕 2. Testando primeiro acesso - ${user.name}...`);
  
  try {
    const response = await fetch(`${BASE_URL}/api/external/generate-or-get-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId: user.userId,
        name: user.name,
        email: user.email,
        secretaria: user.secretaria,
        cargo: user.cargo,
        systemKey: SYSTEM_KEY
      })
    });

    const data = await response.json();
    
    if (data.success && data.isFirstAccess === true) {
      console.log('✅ Primeiro acesso criado com sucesso');
      console.log(`   URL: ${data.chatbotUrl}`);
      return { success: true, chatbotUrl: data.chatbotUrl };
    } else {
      console.log('❌ Falha no primeiro acesso:', data);
      return { success: false };
    }
  } catch (error) {
    console.log('❌ Erro no primeiro acesso:', error);
    return { success: false };
  }
}

async function testExistingAccess(user: TestUser) {
  console.log(`\n🔄 3. Testando acesso existente - ${user.name}...`);
  
  try {
    const response = await fetch(`${BASE_URL}/api/external/generate-or-get-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId: user.userId,
        name: user.name,
        email: user.email,
        secretaria: user.secretaria,
        cargo: user.cargo,
        systemKey: SYSTEM_KEY
      })
    });

    const data = await response.json();
    
    if (data.success && data.isFirstAccess === false) {
      console.log('✅ Acesso existente retornado corretamente');
      console.log(`   URL: ${data.chatbotUrl}`);
      return { success: true, chatbotUrl: data.chatbotUrl };
    } else {
      console.log('❌ Falha no acesso existente:', data);
      return { success: false };
    }
  } catch (error) {
    console.log('❌ Erro no acesso existente:', error);
    return { success: false };
  }
}

async function testUserStats(userId: string) {
  console.log(`\n📊 4. Testando estatísticas do usuário ${userId}...`);
  
  try {
    const response = await fetch(`${BASE_URL}/api/external/user-stats/${userId}`, {
      headers: {
        'X-System-Key': SYSTEM_KEY
      }
    });

    const data = await response.json();
    
    if (data.success && data.data) {
      console.log('✅ Estatísticas obtidas com sucesso');
      console.log(`   Nome: ${data.data.name}`);
      console.log(`   Secretaria: ${data.data.secretaria}`);
      console.log(`   Ativo: ${data.data.isActive}`);
      return true;
    } else {
      console.log('❌ Falha ao obter estatísticas:', data);
      return false;
    }
  } catch (error) {
    console.log('❌ Erro ao obter estatísticas:', error);
    return false;
  }
}

async function testAdminEndpoint() {
  console.log('\n👑 5. Testando endpoint admin...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/system/external-users`, {
      headers: {
        'X-Admin-Key': ADMIN_KEY
      }
    });

    const data = await response.json();
    
    if (data.success && Array.isArray(data.data)) {
      console.log(`✅ Lista de usuários obtida com sucesso`);
      console.log(`   Total: ${data.total} usuários`);
      
      // Mostrar alguns detalhes
      data.data.slice(0, 3).forEach((user: any, index: number) => {
        console.log(`   ${index + 1}. ${user.nome} (${user.secretaria})`);
      });
      
      return true;
    } else {
      console.log('❌ Falha no endpoint admin:', data);
      return false;
    }
  } catch (error) {
    console.log('❌ Erro no endpoint admin:', error);
    return false;
  }
}

async function testInvalidSystemKey() {
  console.log('\n🔒 6. Testando chave inválida...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/external/generate-or-get-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId: 'test',
        name: 'Test User',
        email: '<EMAIL>',
        secretaria: 'Test',
        cargo: 'Test',
        systemKey: 'invalid_key'
      })
    });

    const data = await response.json();
    
    if (!data.success && data.error.includes('inválida')) {
      console.log('✅ Chave inválida rejeitada corretamente');
      return true;
    } else {
      console.log('❌ Chave inválida aceita incorretamente:', data);
      return false;
    }
  } catch (error) {
    console.log('❌ Erro no teste de chave inválida:', error);
    return false;
  }
}

async function runIntegrationTests() {
  console.log('🚀 INICIANDO TESTES DE INTEGRAÇÃO SISTEMA PREFEITURA + CHATBOT\n');
  console.log('=' .repeat(70));
  
  const results = {
    health: false,
    firstAccess: 0,
    existingAccess: 0,
    userStats: 0,
    admin: false,
    security: false,
    total: 0
  };

  // 1. Teste de saúde
  results.health = await testHealthEndpoint();
  if (results.health) results.total++;

  // 2. Teste de primeiro acesso para todos os usuários
  for (const user of testUsers) {
    const result = await testFirstAccess(user);
    if (result.success) {
      results.firstAccess++;
      results.total++;
    }
  }

  // 3. Teste de acesso existente
  for (const user of testUsers) {
    const result = await testExistingAccess(user);
    if (result.success) {
      results.existingAccess++;
      results.total++;
    }
  }

  // 4. Teste de estatísticas
  for (const user of testUsers) {
    const success = await testUserStats(user.userId);
    if (success) {
      results.userStats++;
      results.total++;
    }
  }

  // 5. Teste admin
  results.admin = await testAdminEndpoint();
  if (results.admin) results.total++;

  // 6. Teste de segurança
  results.security = await testInvalidSystemKey();
  if (results.security) results.total++;

  // Resumo final
  console.log('\n' + '=' .repeat(70));
  console.log('📋 RESUMO DOS TESTES');
  console.log('=' .repeat(70));
  console.log(`✅ Endpoint de saúde: ${results.health ? 'OK' : 'FALHOU'}`);
  console.log(`✅ Primeiro acesso: ${results.firstAccess}/${testUsers.length} usuários`);
  console.log(`✅ Acesso existente: ${results.existingAccess}/${testUsers.length} usuários`);
  console.log(`✅ Estatísticas: ${results.userStats}/${testUsers.length} usuários`);
  console.log(`✅ Endpoint admin: ${results.admin ? 'OK' : 'FALHOU'}`);
  console.log(`✅ Segurança: ${results.security ? 'OK' : 'FALHOU'}`);
  console.log('=' .repeat(70));
  
  const maxTests = 6 + (testUsers.length * 3); // 6 testes individuais + 3 por usuário
  const percentage = Math.round((results.total / maxTests) * 100);
  
  console.log(`🎯 RESULTADO FINAL: ${results.total}/${maxTests} testes passaram (${percentage}%)`);
  
  if (percentage >= 90) {
    console.log('🎉 INTEGRAÇÃO FUNCIONANDO PERFEITAMENTE!');
  } else if (percentage >= 70) {
    console.log('⚠️  INTEGRAÇÃO FUNCIONANDO COM ALGUMAS FALHAS');  
  } else {
    console.log('❌ INTEGRAÇÃO COM PROBLEMAS GRAVES');
  }
  
  console.log('\n📝 Próximos passos:');
  console.log('1. Verificar logs do servidor para mais detalhes');
  console.log('2. Testar no frontend admin (aba "Usuários Sistema")');
  console.log('3. Implementar código JavaScript no sistema da prefeitura');
  console.log('4. Configurar CORS se necessário');
  console.log('5. Testar em ambiente de produção');
  
  return percentage >= 90;
}

// Executar testes
runIntegrationTests()
  .then((success) => {
    console.log('\n✨ Testes concluídos.');
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('💥 Erro grave nos testes:', error);
    process.exit(1);
  });