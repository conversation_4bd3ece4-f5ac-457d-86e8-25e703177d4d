import { useState, useEffect } from 'react';

interface TransparencyStats {
  totalServidores: number;
  totalDepartamentos: number;
  totalProtocolos: number;
  protocolosAndamento: number;
  protocolosConcluidos: number;
  tempoMedioTramitacao: number;
}

interface DepartmentStats {
  id: number;
  nome: string;
  totalServidores: number;
  totalProtocolos: number;
  protocolosAndamento: number;
  protocolosConcluidos: number;
  eficiencia: number;
}

interface ProtocolStatus {
  situacao: string;
  total: number;
  percentual: number;
}

interface MonthlyVolume {
  mes: string;
  ano: number;
  total: number;
  crescimento: number;
}

interface PopularSubject {
  assunto: string;
  total: number;
  ranking: number;
}

interface TransparencyDashboard {
  geral: TransparencyStats;
  departamentos: DepartmentStats[];
  statusProtocolos: ProtocolStatus[];
  volumeMensal: MonthlyVolume[];
  assuntosPopulares: PopularSubject[];
  ultimaAtualizacao: string;
}

interface PublicProtocolInfo {
  numeroProtocolo: string;
  dataProtocolo: string;
  situacao: string;
  assunto: string;
  ultimaAtualizacao: string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export function useTransparency() {
  const [dashboard, setDashboard] = useState<TransparencyDashboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Função para buscar dashboard completo
  const fetchDashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE_URL}/api/public/dashboard`);
      
      if (!response.ok) {
        throw new Error(`Erro ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setDashboard(result.data);
        setLastUpdate(new Date());
      } else {
        throw new Error(result.message || 'Erro ao buscar dados');
      }

    } catch (err: any) {
      console.error('Erro ao buscar dashboard:', err);
      setError(err.message || 'Erro ao carregar dados de transparência');
    } finally {
      setLoading(false);
    }
  };

  // Função para consultar protocolo específico
  const consultarProtocolo = async (numeroProtocolo: string): Promise<PublicProtocolInfo | null> => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/public/protocol/${numeroProtocolo}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          return null; // Protocolo não encontrado
        }
        throw new Error(`Erro ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.message || 'Erro ao consultar protocolo');
      }

    } catch (err: any) {
      console.error('Erro ao consultar protocolo:', err);
      throw err;
    }
  };

  // Função para testar conectividade
  const testConnection = async (): Promise<boolean> => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/public/health`);
      const result = await response.json();
      return result.success && result.status === 'healthy';
    } catch (error) {
      return false;
    }
  };

  // Buscar dados automaticamente na primeira carga
  useEffect(() => {
    fetchDashboard();
  }, []);

  // Atualizar dados a cada 5 minutos
  useEffect(() => {
    const interval = setInterval(() => {
      fetchDashboard();
    }, 5 * 60 * 1000); // 5 minutos

    return () => clearInterval(interval);
  }, []);

  return {
    dashboard,
    loading,
    error,
    lastUpdate,
    refetch: fetchDashboard,
    consultarProtocolo,
    testConnection
  };
}

export function useGeneralStats() {
  const [stats, setStats] = useState<TransparencyStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_BASE_URL}/api/public/stats/general`);
        const result = await response.json();
        
        if (result.success) {
          setStats(result.data);
        } else {
          throw new Error(result.message);
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return { stats, loading, error };
}

export function useDepartmentStats() {
  const [departments, setDepartments] = useState<DepartmentStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_BASE_URL}/api/public/stats/departments`);
        const result = await response.json();
        
        if (result.success) {
          setDepartments(result.data);
        } else {
          throw new Error(result.message);
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchDepartments();
  }, []);

  return { departments, loading, error };
}