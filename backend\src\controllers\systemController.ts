import { Request, Response } from 'express';
import { createClient } from 'redis';
import mongoose from 'mongoose';
import { PostgreSQLQueryService } from '../services/PostgreSQLQueryService';
import axios from 'axios';
import fs from 'fs/promises';
import path from 'path';

interface ServiceStatus {
  name: string;
  status: 'online' | 'offline' | 'warning';
  description: string;
  lastUpdate: string;
  latency?: number;
  details?: any;
}

export class SystemController {
  private postgresService: PostgreSQLQueryService;

  constructor() {
    this.postgresService = new PostgreSQLQueryService();
  }

  /**
   * Verificar status de todos os serviços
   */
  async getSystemStatus(req: Request, res: Response) {
    try {
      const statusItems: ServiceStatus[] = [];

      // 1. DeepSeek API
      const deepSeekStatus = await this.checkDeepSeekStatus();
      statusItems.push(deepSeekStatus);

      // 2. Redis Cache
      const redisStatus = await this.checkRedisStatus();
      statusItems.push(redisStatus);

      // 3. PostgreSQL
      const postgresStatus = await this.checkPostgresStatus();
      statusItems.push(postgresStatus);

      // 4. MongoDB
      const mongoStatus = await this.checkMongoStatus();
      statusItems.push(mongoStatus);

      // 5. Sistema RAG/ChromaDB
      const ragStatus = await this.checkRAGStatus();
      statusItems.push(ragStatus);

      // Calcular saúde geral
      const totalServices = statusItems.length;
      const onlineServices = statusItems.filter(s => s.status === 'online').length;
      const warningServices = statusItems.filter(s => s.status === 'warning').length;
      const healthPercentage = Math.round((onlineServices / totalServices) * 100);

      res.json({
        success: true,
        data: {
          services: statusItems,
          summary: {
            total: totalServices,
            online: onlineServices,
            warning: warningServices,
            offline: totalServices - onlineServices - warningServices,
            healthPercentage,
            overallStatus: healthPercentage >= 80 ? 'operational' : 
                          healthPercentage >= 50 ? 'degraded' : 'critical'
          },
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Erro ao verificar status do sistema:', error);
      res.status(500).json({
        success: false,
        error: 'Erro ao verificar status do sistema'
      });
    }
  }

  /**
   * Buscar logs do sistema
   */
  async getSystemLogs(req: Request, res: Response) {
    try {
      const { 
        level = 'all', 
        limit = 50, 
        offset = 0,
        startDate,
        endDate 
      } = req.query;

      // Buscar logs do arquivo de logs
      const logsDir = path.join(__dirname, '../../../logs');
      const logFiles = await fs.readdir(logsDir);
      
      // Filtrar arquivos de log por data se necessário
      const relevantFiles = logFiles.filter(file => {
        if (file.startsWith('app-') && file.endsWith('.log')) {
          if (startDate || endDate) {
            const fileDate = file.match(/app-(\d{4}-\d{2}-\d{2})\.log/)?.[1];
            if (fileDate) {
              const fileDateObj = new Date(fileDate);
              if (startDate && fileDateObj < new Date(startDate as string)) return false;
              if (endDate && fileDateObj > new Date(endDate as string)) return false;
            }
          }
          return true;
        }
        return false;
      });

      // Ler logs dos arquivos relevantes
      const allLogs: any[] = [];
      
      for (const file of relevantFiles.slice(-3)) { // Últimos 3 arquivos
        const filePath = path.join(logsDir, file);
        const content = await fs.readFile(filePath, 'utf-8');
        const lines = content.split('\n').filter(line => line.trim());
        
        for (const line of lines) {
          try {
            const log = JSON.parse(line);
            if (level === 'all' || log.level === level) {
              allLogs.push({
                id: `${file}-${allLogs.length}`,
                timestamp: new Date(log.timestamp),
                level: log.level,
                message: log.message,
                source: log.service || 'System',
                details: log.meta || {}
              });
            }
          } catch (e) {
            // Ignorar linhas que não são JSON válido
          }
        }
      }

      // Ordenar por timestamp decrescente
      allLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      // Aplicar paginação
      const paginatedLogs = allLogs.slice(
        Number(offset), 
        Number(offset) + Number(limit)
      );

      res.json({
        success: true,
        data: {
          logs: paginatedLogs,
          pagination: {
            total: allLogs.length,
            limit: Number(limit),
            offset: Number(offset),
            hasMore: allLogs.length > Number(offset) + Number(limit)
          }
        }
      });
    } catch (error) {
      console.error('Erro ao buscar logs do sistema:', error);
      res.status(500).json({
        success: false,
        error: 'Erro ao buscar logs do sistema'
      });
    }
  }

  /**
   * Verificar status do DeepSeek
   */
  private async checkDeepSeekStatus(): Promise<ServiceStatus> {
    try {
      const startTime = Date.now();
      const response = await axios.post(
        'https://api.deepseek.com/v1/chat/completions',
        {
          model: 'deepseek-chat',
          messages: [{ role: 'user', content: 'test' }],
          max_tokens: 1
        },
        {
          headers: {
            'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
            'Content-Type': 'application/json'
          },
          timeout: 5000
        }
      );
      
      const latency = Date.now() - startTime;

      return {
        name: 'DeepSeek API',
        status: latency < 2000 ? 'online' : 'warning',
        description: `Latência: ${latency}ms`,
        lastUpdate: '1 min atrás',
        latency,
        details: { 
          model: 'deepseek-chat',
          responseTime: `${latency}ms`
        }
      };
    } catch (error) {
      return {
        name: 'DeepSeek API',
        status: 'offline',
        description: 'Conexão falhou',
        lastUpdate: 'agora',
        details: { error: error.message }
      };
    }
  }

  /**
   * Verificar status do Redis
   */
  private async checkRedisStatus(): Promise<ServiceStatus> {
    try {
      const redis = createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379'
      });
      
      await redis.connect();
      const startTime = Date.now();
      await redis.ping();
      const latency = Date.now() - startTime;

      // Buscar estatísticas do cache
      const info = await redis.info('stats');
      const keyspaceInfo = await redis.info('keyspace');
      
      // Calcular hit rate
      const stats = this.parseRedisInfo(info);
      const hitRate = stats.keyspace_hits && stats.keyspace_misses
        ? Math.round((stats.keyspace_hits / (stats.keyspace_hits + stats.keyspace_misses)) * 100)
        : 0;

      await redis.disconnect();

      return {
        name: 'Cache Redis',
        status: latency < 50 ? 'online' : 'warning',
        description: `Hit rate: ${hitRate}%`,
        lastUpdate: '30s atrás',
        latency,
        details: {
          hitRate: `${hitRate}%`,
          totalKeys: this.parseRedisInfo(keyspaceInfo).db0?.keys || 0,
          memoryUsage: stats.used_memory_human || 'N/A'
        }
      };
    } catch (error) {
      return {
        name: 'Cache Redis',
        status: 'offline',
        description: 'Conexão falhou',
        lastUpdate: 'agora',
        details: { error: error.message }
      };
    }
  }

  /**
   * Verificar status do PostgreSQL
   */
  private async checkPostgresStatus(): Promise<ServiceStatus> {
    try {
      const startTime = Date.now();
      
      // Testar conexão básica
      await this.postgresService.testarConexao();
      const latency = Date.now() - startTime;

      // Buscar estatísticas gerais
      const stats = await this.postgresService.obterEstatisticasGerais();

      return {
        name: 'PostgreSQL',
        status: latency < 100 ? 'online' : 'warning',
        description: `${latency}ms de latência`,
        lastUpdate: '15s atrás',
        latency,
        details: {
          totalProtocolos: stats.protocolos,
          totalRequisicoes: stats.requisicoes,
          servicosAtivos: stats.servicos,
          departamentos: stats.departamentos
        }
      };
    } catch (error) {
      return {
        name: 'PostgreSQL',
        status: 'offline',
        description: 'Conexão falhou',
        lastUpdate: 'agora',
        details: { error: error.message }
      };
    }
  }

  /**
   * Verificar status do MongoDB
   */
  private async checkMongoStatus(): Promise<ServiceStatus> {
    try {
      const startTime = Date.now();
      const state = mongoose.connection.readyState;
      const latency = Date.now() - startTime;

      const statusMap = {
        0: 'offline',
        1: 'online',
        2: 'warning',
        3: 'warning'
      };

      const descriptionMap = {
        0: 'Desconectado',
        1: 'Conectado',
        2: 'Conectando',
        3: 'Desconectando'
      };

      return {
        name: 'MongoDB',
        status: statusMap[state] as 'online' | 'offline' | 'warning',
        description: descriptionMap[state],
        lastUpdate: '45s atrás',
        latency,
        details: {
          readyState: state,
          host: mongoose.connection.host,
          name: mongoose.connection.name
        }
      };
    } catch (error) {
      return {
        name: 'MongoDB',
        status: 'offline',
        description: 'Erro ao verificar',
        lastUpdate: 'agora',
        details: { error: error.message }
      };
    }
  }

  /**
   * Verificar status do sistema RAG
   */
  private async checkRAGStatus(): Promise<ServiceStatus> {
    try {
      // Por enquanto, retornar status baseado em arquivo
      const ragDataPath = path.join(__dirname, '../../../rag-data/vector-store.json');
      const exists = await fs.access(ragDataPath).then(() => true).catch(() => false);

      if (exists) {
        const stats = await fs.stat(ragDataPath);
        const lastModified = new Date(stats.mtime);
        const ageInMinutes = Math.round((Date.now() - lastModified.getTime()) / 60000);

        return {
          name: 'Sistema RAG',
          status: ageInMinutes < 60 ? 'online' : 'warning',
          description: exists ? 'Vector store ativo' : 'Vector store não encontrado',
          lastUpdate: `${ageInMinutes} min atrás`,
          details: {
            vectorStoreSize: `${(stats.size / 1024 / 1024).toFixed(2)} MB`,
            lastUpdate: lastModified.toISOString()
          }
        };
      } else {
        return {
          name: 'Sistema RAG',
          status: 'offline',
          description: 'Vector store não encontrado',
          lastUpdate: 'N/A',
          details: { error: 'Arquivo não existe' }
        };
      }
    } catch (error) {
      return {
        name: 'Sistema RAG',
        status: 'offline',
        description: 'Erro ao verificar',
        lastUpdate: 'agora',
        details: { error: error.message }
      };
    }
  }

  /**
   * Parse Redis INFO output
   */
  private parseRedisInfo(info: string): any {
    const result: any = {};
    const lines = info.split('\r\n');
    
    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = value;
        
        // Parse db0 info
        if (key === 'db0') {
          const dbInfo = value.match(/keys=(\d+),expires=(\d+)/);
          if (dbInfo) {
            result.db0 = {
              keys: parseInt(dbInfo[1]),
              expires: parseInt(dbInfo[2])
            };
          }
        }
      }
    }
    
    return result;
  }
}

export default new SystemController();