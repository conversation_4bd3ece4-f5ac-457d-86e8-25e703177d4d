/**
 * Script para testar as APIs de transparência
 */

import transparencyService from '../services/transparencyService';
import chalk from 'chalk';

async function testTransparencyAPIs() {
  console.log(chalk.blue('\n=== TESTE DAS APIs DE TRANSPARÊNCIA ===\n'));

  try {
    // 1. Teste de conexão
    console.log(chalk.yellow('1️⃣ Testando conexão com banco...'));
    const isConnected = await transparencyService.testConnection();
    
    if (isConnected) {
      console.log(chalk.green('✅ Conexão estabelecida com sucesso!'));
    } else {
      console.log(chalk.red('❌ Falha na conexão com banco de dados'));
      return;
    }

    // 2. Estatísticas gerais
    console.log(chalk.yellow('\n2️⃣ Testando estatísticas gerais...'));
    const generalStats = await transparencyService.getGeneralStats();
    console.log(chalk.cyan('📊 Estatísticas Gerais:'));
    console.log(`   - Servidores Ativos: ${generalStats.totalServidores.toLocaleString('pt-BR')}`);
    console.log(`   - Departamentos: ${generalStats.totalDepartamentos.toLocaleString('pt-BR')}`);
    console.log(`   - Protocolos (Ano): ${generalStats.totalProtocolos.toLocaleString('pt-BR')}`);
    console.log(`   - Em Andamento: ${generalStats.protocolosAndamento.toLocaleString('pt-BR')}`);
    console.log(`   - Concluídos: ${generalStats.protocolosConcluidos.toLocaleString('pt-BR')}`);
    console.log(`   - Tempo Médio: ${generalStats.tempoMedioTramitacao} dias`);

    // 3. Estatísticas por departamento
    console.log(chalk.yellow('\n3️⃣ Testando estatísticas por departamento...'));
    const departmentStats = await transparencyService.getDepartmentStats();
    console.log(chalk.cyan(`📈 Top 5 Departamentos (${departmentStats.length} total):`));
    departmentStats.slice(0, 5).forEach((dept, index) => {
      console.log(`   ${index + 1}. ${dept.nome}`);
      console.log(`      - Servidores: ${dept.totalServidores}`);
      console.log(`      - Protocolos: ${dept.totalProtocolos}`);
      console.log(`      - Eficiência: ${dept.eficiencia}%`);
    });

    // 4. Distribuição de status
    console.log(chalk.yellow('\n4️⃣ Testando distribuição de status...'));
    const statusDistribution = await transparencyService.getProtocolStatusDistribution();
    console.log(chalk.cyan('📋 Status dos Protocolos:'));
    statusDistribution.slice(0, 5).forEach((status, index) => {
      console.log(`   ${index + 1}. ${status.situacao}: ${status.total} (${status.percentual}%)`);
    });

    // 5. Volume mensal
    console.log(chalk.yellow('\n5️⃣ Testando volume mensal...'));
    const monthlyVolume = await transparencyService.getMonthlyVolume();
    console.log(chalk.cyan('📅 Volume Mensal (últimos meses):'));
    monthlyVolume.slice(0, 6).forEach((month, index) => {
      const arrow = month.crescimento > 0 ? '📈' : month.crescimento < 0 ? '📉' : '➡️';
      console.log(`   ${index + 1}. ${month.mes}/${month.ano}: ${month.total} ${arrow} ${month.crescimento}%`);
    });

    // 6. Assuntos populares
    console.log(chalk.yellow('\n6️⃣ Testando assuntos populares...'));
    const popularSubjects = await transparencyService.getPopularSubjects();
    console.log(chalk.cyan('🏆 Top 5 Assuntos Mais Solicitados:'));
    popularSubjects.slice(0, 5).forEach((subject) => {
      console.log(`   ${subject.ranking}. ${subject.assunto}: ${subject.total} protocolos`);
    });

    // 7. Teste de consulta de protocolo (se houver dados)
    console.log(chalk.yellow('\n7️⃣ Testando consulta de protocolo...'));
    
    if (generalStats.totalProtocolos > 0) {
      // Tentar buscar um protocolo que provavelmente existe
      const testProtocol = '2024000001'; // Protocolo hipotético
      console.log(chalk.gray(`   Testando protocolo: ${testProtocol}`));
      
      const protocolInfo = await transparencyService.getPublicProtocolInfo(testProtocol);
      
      if (protocolInfo) {
        console.log(chalk.green('✅ Protocolo encontrado:'));
        console.log(`   - Número: ${protocolInfo.numeroProtocolo}`);
        console.log(`   - Data: ${new Date(protocolInfo.dataProtocolo).toLocaleDateString('pt-BR')}`);
        console.log(`   - Situação: ${protocolInfo.situacao}`);
        console.log(`   - Assunto: ${protocolInfo.assunto}`);
      } else {
        console.log(chalk.orange('⚠️  Protocolo de teste não encontrado (esperado para teste)'));
      }
    } else {
      console.log(chalk.orange('⚠️  Nenhum protocolo encontrado para teste'));
    }

    // 8. Resumo final
    console.log(chalk.green('\n✅ TODOS OS TESTES CONCLUÍDOS COM SUCESSO!'));
    console.log(chalk.blue('\n📊 RESUMO DOS DADOS:'));
    console.log(`   • ${generalStats.totalServidores.toLocaleString('pt-BR')} servidores municipais`);
    console.log(`   • ${generalStats.totalDepartamentos.toLocaleString('pt-BR')} departamentos ativos`);
    console.log(`   • ${generalStats.totalProtocolos.toLocaleString('pt-BR')} protocolos no último ano`);
    console.log(`   • ${departmentStats.length} departamentos com dados`);
    console.log(`   • ${statusDistribution.length} tipos de situação`);
    console.log(`   • ${monthlyVolume.length} meses de histórico`);
    console.log(`   • ${popularSubjects.length} tipos de assunto`);

    console.log(chalk.magenta('\n🚀 APIs prontas para uso no Portal de Transparência!'));

  } catch (error: any) {
    console.error(chalk.red('\n❌ ERRO NOS TESTES:'));
    console.error(chalk.red(error.message));
    console.error(error);
  }
}

// Executar testes
testTransparencyAPIs()
  .then(() => {
    console.log(chalk.gray('\n🏁 Testes finalizados.'));
    process.exit(0);
  })
  .catch((error) => {
    console.error(chalk.red('\n💥 Erro fatal nos testes:'), error);
    process.exit(1);
  });