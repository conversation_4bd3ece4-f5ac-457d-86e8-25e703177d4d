'use client';

import { useState } from 'react';
import {
  SidebarProvider,
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
  CustomResizablePanel
} from '@/components/ui';
import ThemeToggle from '@/components/ThemeToggle';
import {
  AdminSidebar,
  ChatPanel,
  DashboardSection,
  MetricsIASection,
  DadosMunicipaisSection,
  TokenManager,
  SystemStatus,
  LogsViewer
} from '@/components/admin';
import { ExternalUsers } from '@/components/admin/ExternalUsers';
import { useChat } from '@/hooks/useChat';
import { useMetrics } from '@/hooks/useMetrics';
import { usePostgresMetrics } from '@/hooks/usePostgresData';
import Image from 'next/image';

export default function AdminPage() {
  const [activeSection, setActiveSection] = useState('dashboard');
  const [showChat, setShowChat] = useState(true);
  const [chatExpanded, setChatExpanded] = useState(false);

  // Admin user (para o chat usar contexto administrativo)
  const adminUser = { id: 'admin', name: '<PERSON><PERSON><PERSON><PERSON>', role: 'admin' };
  const adminToken = 'admin-context'; // Token especial para admin

  const { messages, isLoading, sendMessage, clearMessages } = useChat(adminToken, adminUser);
  const { metrics, isLoading: metricsLoading } = useMetrics();
  const {
    metrics: postgresMetrics,
    protocolosRecentes,
    alvarasRecentes,
    topDepartamentos,
    isConnected: postgresConnected,
    loading: postgresLoading
  } = usePostgresMetrics();

  // Função de logout (redireciona para home)
  const logout = () => {
    window.location.href = '/';
  };

  const handleSendMessage = async (message: string) => {
    try {
      await sendMessage(message);
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  const handleClearChat = () => {
    clearMessages();
  };

  // Renderizar conteúdo baseado na seção ativa
  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <DashboardSection
            metrics={metrics}
            postgresMetrics={postgresMetrics}
            postgresConnected={postgresConnected}
          />
        );
      case 'metrics-ia':
        return (
          <MetricsIASection
            metrics={metrics}
            isLoading={metricsLoading}
          />
        );
      case 'dados-municipais':
        return (
          <DadosMunicipaisSection
            postgresMetrics={postgresMetrics}
            protocolosRecentes={protocolosRecentes}
            alvarasRecentes={alvarasRecentes}
            topDepartamentos={topDepartamentos}
            isConnected={postgresConnected}
            loading={postgresLoading}
          />
        );
      case 'tokens':
        return <TokenManager />;
      case 'external-users':
        return <ExternalUsers />;
      case 'logs':
        return (
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <svg className="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  Logs do Sistema
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  Monitoramento e logs de atividade
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              <SystemStatus />
              <LogsViewer />
            </div>
          </div>
        );
      default:
        return (
          <DashboardSection
            metrics={metrics}
            postgresMetrics={postgresMetrics}
            postgresConnected={postgresConnected}
          />
        );
    }
  };

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
        {/* Sidebar */}
        <AdminSidebar
          activeSection={activeSection}
          onSectionChange={setActiveSection}
          onLogout={logout}
        />

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col min-h-screen">
          {/* Top Header */}
          <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm z-10">
            <div className="px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Image
                    src="/images/logo/V.png"
                    alt="Logo Prefeitura Virtual"
                    width={40}
                    height={40}
                    priority
                    className="object-contain"
                  />
                  <div>
                    <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                      Painel Administrativo
                    </h1>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Prefeitura Virtual IA
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  {/* Chat Toggle */}
                  <button
                    onClick={() => setShowChat(!showChat)}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                      showChat
                        ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200'
                    }`}
                    title={showChat ? "Ocultar Chat" : "Mostrar Chat"}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span className="text-sm font-medium">Chat</span>
                  </button>

                  <ThemeToggle />
                </div>
              </div>
            </div>
          </header>

          {/* Content Area with Responsive Layout */}
          <div className="flex-1 flex overflow-hidden">
            {/* Main Content Panel - Always Full Width */}
            <div className="flex-1 overflow-auto">
              <div className="p-4 md:p-6 lg:p-8 w-full h-full">
                {renderContent()}
              </div>
            </div>

            {/* Chat Panel - Resizable */}
            {showChat && (
              <CustomResizablePanel
                minWidth={350}
                maxWidth={800}
                defaultWidth={500}
                className="border-l border-gray-200 dark:border-gray-700 transition-all duration-300"
              >
                <div className="h-full p-4 lg:p-6">
                  <ChatPanel
                    messages={messages}
                    isLoading={isLoading}
                    onSendMessage={handleSendMessage}
                    onClearChat={handleClearChat}
                    isExpanded={chatExpanded}
                    onToggleExpand={() => setChatExpanded(!chatExpanded)}
                    onClose={() => setShowChat(false)}
                  />
                </div>
              </CustomResizablePanel>
            )}
          </div>

          {/* Chat Expandido */}
          {chatExpanded && (
            <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
              <div className="w-full max-w-6xl h-full max-h-[90vh] bg-white dark:bg-gray-800 rounded-lg shadow-2xl">
                <ChatPanel
                  messages={messages}
                  isLoading={isLoading}
                  onSendMessage={handleSendMessage}
                  onClearChat={handleClearChat}
                  isExpanded={chatExpanded}
                  onToggleExpand={() => setChatExpanded(!chatExpanded)}
                  onClose={() => setShowChat(false)}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </SidebarProvider>
  );
}