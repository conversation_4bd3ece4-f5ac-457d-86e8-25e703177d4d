#!/usr/bin/env tsx

/**
 * Script para listar todos os tokens de acesso ativos
 * Uso: npm run list-tokens
 */

import { accessTokenService } from '../services/accessTokenService';
import { postgresRepository } from '../repositories/PostgreSQLRepository';

async function listAccessTokens() {
  console.log('📋 Listando tokens de acesso ativos...\n');

  try {
    // Listar tokens
    const tokens = await accessTokenService.listActiveTokens();

    if (tokens.length === 0) {
      console.log('ℹ️  Nenhum token de acesso encontrado.');
      console.log('💡 Use o script create-access-token.ts para criar um novo token.\n');
      return;
    }

    console.log(`✅ Encontrados ${tokens.length} tokens ativos:\n`);

    // Exibir cada token
    tokens.forEach((token, index) => {
      console.log(`${index + 1}. 🔑 TOKEN #${token.userId}`);
      console.log(`   📝 Descrição: ${token.description}`);
      console.log(`   🆔 User ID: ${token.userId}`);
      console.log(`   📅 Criado: ${token.createdAt.toLocaleString('pt-BR')}`);
      console.log(`   🔑 Token: ${token.token.substring(0, 8)}...${token.token.substring(token.token.length - 4)}`);
      console.log(`   🔗 Link: ${accessTokenService.generateAccessLink(token.token)}`);
      console.log(`   ✅ Status: ${token.isActive ? 'Ativo' : 'Inativo'}`);
      console.log('   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    });
    
    console.log('');

    // Estatísticas
    const activeCount = tokens.filter(t => t.isActive).length;
    const inactiveCount = tokens.length - activeCount;

    console.log('📊 ESTATÍSTICAS:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`🟢 Tokens ativos: ${activeCount}`);
    console.log(`🔴 Tokens inativos: ${inactiveCount}`);
    console.log(`📈 Total: ${tokens.length}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

    // Informações sobre como gerenciar
    console.log('🛠️  GERENCIAMENTO:');
    console.log('• Para criar novo token: npm run create-token "Descrição"');
    console.log('• Para revogar token: npm run revoke-token <token>');
    console.log('• Para ver conversas de um token: npm run token-stats <token>\n');

  } catch (error) {
    console.error('❌ Erro ao listar tokens:', error);
    process.exit(1);
  }
}

// Executar apenas se chamado diretamente
if (require.main === module) {
  listAccessTokens().then(() => {
    console.log('🎉 Listagem concluída!');
    process.exit(0);
  }).catch((error) => {
    console.error('\n💥 Erro fatal:', error);
    process.exit(1);
  });
}