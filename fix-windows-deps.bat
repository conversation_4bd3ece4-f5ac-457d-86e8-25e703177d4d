@echo off
echo 🔄 Corrigindo dependências para Windows...

echo.
echo 1. Limpando node_modules e lock files...
if exist node_modules rmdir /s /q node_modules
if exist backend\node_modules rmdir /s /q backend\node_modules
if exist frontend\node_modules rmdir /s /q frontend\node_modules
if exist package-lock.json del package-lock.json
if exist backend\package-lock.json del backend\package-lock.json
if exist frontend\package-lock.json del frontend\package-lock.json

echo.
echo 2. Instalando dependências root...
call npm install

echo.
echo 3. Instalando dependências backend...
cd backend
call npm install
cd ..

echo.
echo 4. Instalando dependências frontend...
cd frontend
call npm install
cd ..

echo.
echo ✅ Instalações concluídas! Testando backend...
cd backend
echo.
echo 🚀 Iniciando servidor de desenvolvimento...
call npm run dev

pause