import { useEffect, useRef } from 'react';
import { ChatMessage } from '@/types';
import { MessageBubble } from './MessageBubble';
import { clsx } from 'clsx';

interface ChatWindowProps {
  messages: ChatMessage[];
  isLoading?: boolean;
  className?: string;
  onQuickAction?: (action: string) => void;
}

export function ChatWindow({ messages, isLoading = false, className, onQuickAction }: ChatWindowProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Quick action messages
  const quickActions = {
    departments: "Listar todos os departamentos municipais ativos",
    employees: "Buscar funcionários ativos da prefeitura",
    reports: "Gerar relatório de serviços municipais",
    search: "Como posso ajudar você hoje?"
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div 
      ref={containerRef}
      className={clsx(
        'flex-1 overflow-y-auto bg-white dark:bg-gray-800 px-4 py-6 space-y-4',
        className
      )}
    >
      {messages.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full text-center">
          <div className="mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-cyan-500 rounded-full flex items-center justify-center mb-4 mx-auto">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2">
              Bem-vindo ao Chatbot da Prefeitura Virtual
            </h3>
            <p className="text-gray-600 dark:text-gray-300 max-w-md">
              Faça perguntas sobre serviços municipais, consulte informações dos departamentos ou solicite relatórios específicos.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl">
            <button 
              onClick={() => onQuickAction?.(quickActions.departments)}
              className="p-3 text-left bg-blue-50 dark:bg-blue-900 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-lg border border-blue-200 dark:border-blue-700 transition-colors"
            >
              <div className="font-medium text-blue-800 dark:text-blue-200">💼 Consultar Departamentos</div>
              <div className="text-sm text-blue-600 dark:text-blue-300">Ver informações dos departamentos municipais</div>
            </button>
            
            <button 
              onClick={() => onQuickAction?.(quickActions.employees)}
              className="p-3 text-left bg-cyan-50 dark:bg-cyan-900 hover:bg-cyan-100 dark:hover:bg-cyan-800 rounded-lg border border-cyan-200 dark:border-cyan-700 transition-colors"
            >
              <div className="font-medium text-cyan-800 dark:text-cyan-200">👥 Buscar Funcionários</div>
              <div className="text-sm text-cyan-600 dark:text-cyan-300">Encontrar informações sobre servidores</div>
            </button>
            
            <button 
              onClick={() => onQuickAction?.(quickActions.reports)}
              className="p-3 text-left bg-yellow-50 dark:bg-yellow-900 hover:bg-yellow-100 dark:hover:bg-yellow-800 rounded-lg border border-yellow-200 dark:border-yellow-700 transition-colors"
            >
              <div className="font-medium text-yellow-800 dark:text-yellow-200">📊 Gerar Relatórios</div>
              <div className="text-sm text-yellow-600 dark:text-yellow-300">Solicitar relatórios específicos</div>
            </button>
            
            <button 
              onClick={() => onQuickAction?.(quickActions.search)}
              className="p-3 text-left bg-orange-50 dark:bg-orange-900 hover:bg-orange-100 dark:hover:bg-orange-800 rounded-lg border border-orange-200 dark:border-orange-700 transition-colors"
            >
              <div className="font-medium text-orange-800 dark:text-orange-200">🔍 Busca Avançada</div>
              <div className="text-sm text-orange-600 dark:text-orange-300">Fazer consultas personalizadas</div>
            </button>
          </div>
        </div>
      ) : (
        <>
          {messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">IA</span>
                </div>
                <div className="bg-gray-100 dark:bg-gray-700 rounded-lg px-4 py-3 border border-gray-200 dark:border-gray-600">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}
      
      <div ref={messagesEndRef} />
    </div>
  );
}