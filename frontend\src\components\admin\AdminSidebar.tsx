'use client';

import {
  <PERSON><PERSON>,
  <PERSON>barContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar
} from '@/components/ui';
import {
  FaChartLine,
  FaDatabase,
  FaKey,
  FaUsers,
  FaFileAlt,
  FaTachometerAlt,
  FaRobot,
  FaSignOutAlt,
  FaCog,
  FaBars
} from 'react-icons/fa';
import Image from 'next/image';

interface AdminSidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  onLogout: () => void;
}

const navigationItems = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: FaTachometerAlt,
    description: 'Visão geral do sistema',
    color: 'text-blue-500 dark:text-blue-400'
  },
  {
    id: 'metrics-ia',
    label: 'Métricas IA',
    icon: FaRobot,
    description: 'Custos, performance e cache',
    color: 'text-purple-500 dark:text-purple-400'
  },
  {
    id: 'dados-municipais',
    label: 'Dados Municipais',
    icon: FaDatabase,
    description: 'PostgreSQL e protocolos',
    color: 'text-green-500 dark:text-green-400'
  },
  {
    id: 'tokens',
    label: 'Gestão de Tokens',
    icon: FaKey,
    description: 'Tokens de acesso',
    color: 'text-orange-500 dark:text-orange-400'
  },
  {
    id: 'external-users',
    label: 'Usuários Externos',
    icon: FaUsers,
    description: 'Gestão de usuários',
    color: 'text-cyan-500 dark:text-cyan-400'
  },
  {
    id: 'logs',
    label: 'Logs do Sistema',
    icon: FaFileAlt,
    description: 'Monitoramento e logs',
    color: 'text-gray-500 dark:text-gray-400'
  }
];

export function AdminSidebar({ activeSection, onSectionChange, onLogout }: AdminSidebarProps) {
  const { state } = useSidebar();
  const isCollapsed = state === 'collapsed';

  return (
    <Sidebar collapsible="icon" className="border-r border-gray-200 dark:border-gray-700 transition-all duration-300">
      <SidebarHeader className="border-b border-gray-200 dark:border-gray-700 p-4">
        <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'justify-between'} transition-all duration-300`}>
          <div className={`flex items-center ${isCollapsed ? 'flex-col gap-2' : 'space-x-3'}`}>
            <Image
              src="/images/logo/V.png"
              alt="Logo Prefeitura Virtual"
              width={isCollapsed ? 32 : 40}
              height={isCollapsed ? 32 : 40}
              priority
              className="object-contain flex-shrink-0 transition-all duration-300"
            />
            {!isCollapsed && (
              <div className="min-w-0">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                  Admin Panel
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-300 truncate">
                  Prefeitura Virtual IA
                </p>
              </div>
            )}
          </div>
          {!isCollapsed && <SidebarTrigger className="ml-2 flex-shrink-0" />}
        </div>
        {isCollapsed && (
          <div className="mt-3 flex justify-center">
            <SidebarTrigger className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg p-1.5 transition-colors" />
          </div>
        )}
      </SidebarHeader>

      <SidebarContent className="px-2">
        <SidebarGroup className="py-4">
          {!isCollapsed && (
            <SidebarGroupLabel className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
              Navegação Principal
            </SidebarGroupLabel>
          )}
          <SidebarMenu className="space-y-2">
            {navigationItems.map((item) => (
              <SidebarMenuItem key={item.id}>
                <SidebarMenuButton
                  isActive={activeSection === item.id}
                  onClick={() => onSectionChange(item.id)}
                  className={`
                    w-full h-auto min-h-[3rem] rounded-lg transition-all duration-200
                    hover:bg-gray-100 dark:hover:bg-gray-800
                    ${activeSection === item.id
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-l-4 border-blue-500'
                      : 'text-gray-700 dark:text-gray-200'
                    }
                    ${isCollapsed ? 'justify-center px-2 py-3' : 'justify-start px-3 py-2'}
                  `}
                  title={isCollapsed ? item.label : undefined}
                >
                  <item.icon className={`w-5 h-5 ${item.color} flex-shrink-0 ${isCollapsed ? '' : 'mr-3'}`} />
                  {!isCollapsed && (
                    <div className="flex flex-col items-start min-w-0 flex-1">
                      <span className="font-medium text-sm truncate w-full">{item.label}</span>
                      <span className="text-xs text-gray-500 dark:text-gray-400 truncate w-full">
                        {item.description}
                      </span>
                    </div>
                  )}
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup>

        <SidebarGroup className="py-4">
          {!isCollapsed && (
            <SidebarGroupLabel className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
              Sistema
            </SidebarGroupLabel>
          )}
          <SidebarMenu className="space-y-2">
            <SidebarMenuItem>
              <SidebarMenuButton
                className={`
                  w-full h-auto min-h-[3rem] rounded-lg transition-all duration-200
                  hover:bg-gray-100 dark:hover:bg-gray-800
                  text-gray-700 dark:text-gray-200
                  ${isCollapsed ? 'justify-center px-2 py-3' : 'justify-start px-3 py-2'}
                `}
                title={isCollapsed ? 'Configurações' : undefined}
              >
                <FaCog className={`w-5 h-5 text-gray-500 dark:text-gray-400 flex-shrink-0 ${isCollapsed ? '' : 'mr-3'}`} />
                {!isCollapsed && (
                  <div className="flex flex-col items-start min-w-0 flex-1">
                    <span className="font-medium text-sm truncate w-full">Configurações</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 truncate w-full">
                      Configurações gerais
                    </span>
                  </div>
                )}
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t border-gray-200 dark:border-gray-700 p-2">
        <SidebarMenu className="space-y-2">
          <SidebarMenuItem>
            <SidebarMenuButton
              onClick={onLogout}
              className={`
                w-full h-auto min-h-[3rem] rounded-lg transition-all duration-200
                text-red-600 dark:text-red-400
                hover:bg-red-50 dark:hover:bg-red-900/20
                ${isCollapsed ? 'justify-center px-2 py-3' : 'justify-start px-3 py-2'}
              `}
              title={isCollapsed ? 'Sair' : undefined}
            >
              <FaSignOutAlt className={`w-5 h-5 flex-shrink-0 ${isCollapsed ? '' : 'mr-3'}`} />
              {!isCollapsed && (
                <div className="flex flex-col items-start min-w-0 flex-1">
                  <span className="font-medium text-sm truncate w-full">Sair</span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 truncate w-full">
                    Voltar ao início
                  </span>
                </div>
              )}
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>

        {/* Status do Sistema */}
        <div className="p-2 mt-4">
          {isCollapsed ? (
            <div className="flex justify-center">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" title="Sistema Online"></div>
            </div>
          ) : (
            <div className="flex items-center space-x-2 bg-green-50 dark:bg-green-900/20 rounded-lg px-3 py-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-700 dark:text-green-300 font-medium">
                Sistema Online
              </span>
            </div>
          )}
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
