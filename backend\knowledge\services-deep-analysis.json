{"servicos": [{"id": "4", "descricao": "DIVISÃO DE SERVIÇOS GERAIS - SOS", "cobrar_taxa": false, "created_at": "2022-01-15T14:06:20.000Z", "updated_at": "2022-01-27T12:59:09.000Z", "id_departamento": "26", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "5", "descricao": "CREDENCIAMENTO", "cobrar_taxa": false, "created_at": "2022-01-15T14:06:20.000Z", "updated_at": "2022-11-25T11:28:06.000Z", "id_departamento": "22", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "7", "descricao": "ALVARÁ DE FUNCIONAMENTO", "cobrar_taxa": false, "created_at": "2022-01-15T23:47:32.000Z", "updated_at": "2023-06-15T15:14:08.000Z", "id_departamento": "24", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "8", "descricao": "FISCALIZAÇÃO DE POSTURAS", "cobrar_taxa": false, "created_at": "2022-01-15T23:49:54.000Z", "updated_at": "2022-12-19T18:10:08.000Z", "id_departamento": "28", "id_modelo_abertura_exigencia": "66", "id_modelo_despacho_exigencia": "66"}, {"id": "9", "descricao": "ANÁLISE DO PROCESSO", "cobrar_taxa": false, "created_at": "2022-01-15T23:56:16.000Z", "updated_at": "2022-02-07T12:55:05.000Z", "id_departamento": "29", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "10", "descricao": "VIGILÂNCIA SANITÁRIA", "cobrar_taxa": false, "created_at": "2022-01-15T23:58:26.000Z", "updated_at": "2022-01-15T23:58:26.000Z", "id_departamento": "30", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "11", "descricao": "EMISSÃO DO ALVARÁ", "cobrar_taxa": false, "created_at": "2022-01-25T20:30:00.000Z", "updated_at": "2022-01-25T20:30:00.000Z", "id_departamento": "24", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "12", "descricao": "EMISSÃO TAXA ALVARÁ", "cobrar_taxa": false, "created_at": "2022-01-25T20:32:10.000Z", "updated_at": "2022-02-07T18:15:55.000Z", "id_departamento": "39", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "13", "descricao": "CERTIDÕES", "cobrar_taxa": false, "created_at": "2022-01-26T19:59:28.000Z", "updated_at": "2022-01-26T19:59:28.000Z", "id_departamento": "36", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "14", "descricao": "DIVISÃO DE SERVIÇOS GERAIS - TAPA BURACO", "cobrar_taxa": false, "created_at": "2022-01-27T13:10:30.000Z", "updated_at": "2022-01-27T13:10:30.000Z", "id_departamento": "31", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "15", "descricao": "DIVISÃO DE SERVIÇOS GERAIS - BOCA DE LOBO", "cobrar_taxa": false, "created_at": "2022-01-27T13:11:03.000Z", "updated_at": "2022-01-27T13:11:03.000Z", "id_departamento": "32", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "16", "descricao": "DIVISÃO DE SERVIÇOS GERAIS - ROÇAGEM", "cobrar_taxa": false, "created_at": "2022-01-27T13:11:31.000Z", "updated_at": "2022-01-27T13:11:31.000Z", "id_departamento": "33", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "17", "descricao": "DIVISÃO DE SERVIÇOS GERAIS - ILUMINAÇÃO PÚBLICA", "cobrar_taxa": false, "created_at": "2022-01-27T13:12:17.000Z", "updated_at": "2022-01-27T13:12:17.000Z", "id_departamento": "34", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "18", "descricao": "DIVISÃO DE SERVIÇOS GERAIS - RETIRADA DE ENTULHO", "cobrar_taxa": false, "created_at": "2022-01-27T13:12:48.000Z", "updated_at": "2022-01-27T13:12:48.000Z", "id_departamento": "35", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "19", "descricao": "EMISSÃO DE CERTIFICADO", "cobrar_taxa": false, "created_at": "2022-02-02T19:38:02.000Z", "updated_at": "2022-02-02T19:38:02.000Z", "id_departamento": "37", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "20", "descricao": "EMISSÃO DE CERTIFICADO", "cobrar_taxa": false, "created_at": "2022-02-02T19:40:19.000Z", "updated_at": "2022-02-02T19:40:19.000Z", "id_departamento": "37", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "21", "descricao": "ADMISSÃO DE SERVIDOR", "cobrar_taxa": false, "created_at": "2022-02-22T13:40:54.000Z", "updated_at": "2022-02-22T13:40:54.000Z", "id_departamento": "27", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "22", "descricao": "DENÚNCIA FISCALIZAÇÃO DE POSTURAS", "cobrar_taxa": false, "created_at": "2022-03-11T20:14:17.000Z", "updated_at": "2023-01-24T12:53:10.000Z", "id_departamento": "97", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "23", "descricao": "FISCALIZAÇÃO AMBIENTAL", "cobrar_taxa": false, "created_at": "2022-03-28T22:09:22.000Z", "updated_at": "2022-03-28T22:09:22.000Z", "id_departamento": "42", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}, {"id": "24", "descricao": "FISCALIZAÇÃO TRÂNSITO E TRANSPORTE", "cobrar_taxa": false, "created_at": "2022-03-28T22:10:23.000Z", "updated_at": "2022-03-28T22:10:23.000Z", "id_departamento": "43", "id_modelo_abertura_exigencia": null, "id_modelo_despacho_exigencia": null}], "solicitacoes": [], "protocolos": [{"id": "73439", "id_protocolo": "20250060326", "data_protocolo": "2025-07-21T03:00:00.000Z", "departamento_original": "29", "departamento_atual": "85", "id_assunto": "139", "id_sub_assunto": "384", "observacao": "AUTOS EXTRAJUCIAIS 202500231199 -  OFÍCIO 2025004370452 - QUISOQUE CHURRASQUINHO DO CARECA -FLORES DO PLANALTO ( PERTUBAÇÃO DO SOSSEGO).", "id_interessado": "3", "id_usuario": "5348", "tipo_protocolo": "VIRTUAL", "data_ultima_visualizacao": null, "usuario_ultima_visualizacao": null, "created_at": "2025-07-21T17:46:23.000Z", "updated_at": "2025-07-21T17:58:56.000Z", "id_situacao": "1", "caminho_pdf_final": "/home/<USER>/data/protocolo_virtual_processos/73439/2025-07-21/68350b3d-db4a-4ae5-b718-d72769eb4df1.pdf", "data_arquivamento": null, "observacao_arquivamento": null, "id_usuario_arquivamento": null, "sigiloso": false, "arquivo_pdfa_final": "", "local_requisicao": null, "id_protocolo_pai": null, "id_processo_outro_sistema": null}, {"id": "73438", "id_protocolo": "20250060306", "data_protocolo": "2025-07-21T03:00:00.000Z", "departamento_original": "85", "departamento_atual": "85", "id_assunto": "84", "id_sub_assunto": "208", "observacao": "TRATAM OS AUTOS SOBRE PEDIDO PARA INTERVENÇÃO EM ESPÉCIE ARBÓREA SITUADA NA QUADRA 42, CASA 7, ETAPA B – VALPARAÍSO I QUE ESTÁ COM GRANDE RISCO DE QUEDA.", "id_interessado": "421", "id_usuario": "16662", "tipo_protocolo": "VIRTUAL", "data_ultima_visualizacao": null, "usuario_ultima_visualizacao": null, "created_at": "2025-07-21T17:30:44.000Z", "updated_at": "2025-07-21T17:31:38.000Z", "id_situacao": "1", "caminho_pdf_final": null, "data_arquivamento": null, "observacao_arquivamento": null, "id_usuario_arquivamento": null, "sigiloso": false, "arquivo_pdfa_final": null, "local_requisicao": null, "id_protocolo_pai": null, "id_processo_outro_sistema": null}, {"id": "73437", "id_protocolo": "20250060257", "data_protocolo": "2025-07-21T03:00:00.000Z", "departamento_original": "357", "departamento_atual": "45", "id_assunto": "9", "id_sub_assunto": "3", "observacao": "", "id_interessado": "34137", "id_usuario": "5725", "tipo_protocolo": "SERVICOS_ONLINE", "data_ultima_visualizacao": null, "usuario_ultima_visualizacao": null, "created_at": "2025-07-21T16:09:54.000Z", "updated_at": "2025-07-21T16:09:55.000Z", "id_situacao": "1", "caminho_pdf_final": null, "data_arquivamento": null, "observacao_arquivamento": null, "id_usuario_arquivamento": null, "sigiloso": false, "arquivo_pdfa_final": null, "local_requisicao": "INTERNO", "id_protocolo_pai": null, "id_processo_outro_sistema": null}, {"id": "73436", "id_protocolo": "20250060247", "data_protocolo": "2025-07-21T03:00:00.000Z", "departamento_original": "357", "departamento_atual": "39", "id_assunto": "112", "id_sub_assunto": "284", "observacao": "", "id_interessado": "37156", "id_usuario": "113690", "tipo_protocolo": "SERVICOS_ONLINE", "data_ultima_visualizacao": null, "usuario_ultima_visualizacao": null, "created_at": "2025-07-21T15:08:49.000Z", "updated_at": "2025-07-21T15:08:51.000Z", "id_situacao": "1", "caminho_pdf_final": null, "data_arquivamento": null, "observacao_arquivamento": null, "id_usuario_arquivamento": null, "sigiloso": false, "arquivo_pdfa_final": null, "local_requisicao": "EXTERNO", "id_protocolo_pai": null, "id_processo_outro_sistema": null}, {"id": "73435", "id_protocolo": "20250060228", "data_protocolo": "2025-07-21T03:00:00.000Z", "departamento_original": "357", "departamento_atual": "39", "id_assunto": "117", "id_sub_assunto": "309", "observacao": "", "id_interessado": "14468", "id_usuario": "4721", "tipo_protocolo": "SERVICOS_ONLINE", "data_ultima_visualizacao": null, "usuario_ultima_visualizacao": null, "created_at": "2025-07-21T14:20:05.000Z", "updated_at": "2025-07-21T14:21:14.000Z", "id_situacao": "1", "caminho_pdf_final": "/home/<USER>/data/protocolo_virtual_processos/73435/2025-07-21/61f547aa-2eb3-4c72-9d69-fa9a11fbc69e.pdf", "data_arquivamento": null, "observacao_arquivamento": null, "id_usuario_arquivamento": null, "sigiloso": false, "arquivo_pdfa_final": "", "local_requisicao": "EXTERNO", "id_protocolo_pai": null, "id_processo_outro_sistema": null}, {"id": "73434", "id_protocolo": "20250060196", "data_protocolo": "2025-07-21T03:00:00.000Z", "departamento_original": "172", "departamento_atual": "84", "id_assunto": "6", "id_sub_assunto": "148", "observacao": null, "id_interessado": "34330", "id_usuario": "26763", "tipo_protocolo": "VIRTUAL", "data_ultima_visualizacao": null, "usuario_ultima_visualizacao": null, "created_at": "2025-07-21T13:33:51.000Z", "updated_at": "2025-07-21T13:41:49.000Z", "id_situacao": "1", "caminho_pdf_final": "/home/<USER>/data/protocolo_virtual_processos/73434/2025-07-21/87823836-32bd-4915-b318-7616942ac7e9.pdf", "data_arquivamento": null, "observacao_arquivamento": null, "id_usuario_arquivamento": null, "sigiloso": false, "arquivo_pdfa_final": "", "local_requisicao": null, "id_protocolo_pai": null, "id_processo_outro_sistema": null}, {"id": "73433", "id_protocolo": "20250060187", "data_protocolo": "2025-07-21T03:00:00.000Z", "departamento_original": "85", "departamento_atual": "85", "id_assunto": "104", "id_sub_assunto": "264", "observacao": "UMA CADELA PRETA DE PORTE MÉDIO QUE PODE TER SOFRIDO AGRESSÃO, DEMONSTR<PERSON>DO MACHUCADOS EM SUA CABEÇA E BOCA \nPRAÇA DA ETAPA A", "id_interessado": "232", "id_usuario": "101867", "tipo_protocolo": "VIRTUAL", "data_ultima_visualizacao": null, "usuario_ultima_visualizacao": null, "created_at": "2025-07-21T13:21:44.000Z", "updated_at": "2025-07-21T13:21:44.000Z", "id_situacao": "1", "caminho_pdf_final": null, "data_arquivamento": null, "observacao_arquivamento": null, "id_usuario_arquivamento": null, "sigiloso": false, "arquivo_pdfa_final": null, "local_requisicao": null, "id_protocolo_pai": null, "id_processo_outro_sistema": null}, {"id": "73432", "id_protocolo": "20250060173", "data_protocolo": "2025-07-21T03:00:00.000Z", "departamento_original": "491", "departamento_atual": "85", "id_assunto": "84", "id_sub_assunto": "208", "observacao": "SOLICITAÇÃO PARA INTERVENÇÃO EM UMA ESPÉCIE ARBÓREA NA NOVA VILA GUAÍRA.", "id_interessado": "716", "id_usuario": "6990", "tipo_protocolo": "VIRTUAL", "data_ultima_visualizacao": null, "usuario_ultima_visualizacao": null, "created_at": "2025-07-21T13:08:52.000Z", "updated_at": "2025-07-21T13:11:26.000Z", "id_situacao": "1", "caminho_pdf_final": "/home/<USER>/data/protocolo_virtual_processos/73432/2025-07-21/5ecc8493-04a7-4e8e-a0ba-300389412c47.pdf", "data_arquivamento": null, "observacao_arquivamento": null, "id_usuario_arquivamento": null, "sigiloso": false, "arquivo_pdfa_final": "", "local_requisicao": null, "id_protocolo_pai": null, "id_processo_outro_sistema": null}, {"id": "73431", "id_protocolo": "20250060168", "data_protocolo": "2025-07-21T03:00:00.000Z", "departamento_original": "357", "departamento_atual": "451", "id_assunto": "8", "id_sub_assunto": "257", "observacao": "", "id_interessado": "37154", "id_usuario": "8254", "tipo_protocolo": "SERVICOS_ONLINE", "data_ultima_visualizacao": "2025-07-21T17:26:42.000Z", "usuario_ultima_visualizacao": "5818", "created_at": "2025-07-21T13:07:03.000Z", "updated_at": "2025-07-21T17:26:42.000Z", "id_situacao": "1", "caminho_pdf_final": "/home/<USER>/data/protocolo_virtual_processos/73431/2025-07-21/f19721d6-3b89-4c4c-8e29-3b0178535667.pdf", "data_arquivamento": null, "observacao_arquivamento": null, "id_usuario_arquivamento": null, "sigiloso": false, "arquivo_pdfa_final": "", "local_requisicao": "EXTERNO", "id_protocolo_pai": null, "id_processo_outro_sistema": null}, {"id": "73430", "id_protocolo": "20250060156", "data_protocolo": "2025-07-21T03:00:00.000Z", "departamento_original": "357", "departamento_atual": "450", "id_assunto": "8", "id_sub_assunto": "256", "observacao": "", "id_interessado": "36772", "id_usuario": "113133", "tipo_protocolo": "SERVICOS_ONLINE", "data_ultima_visualizacao": "2025-07-21T17:11:53.000Z", "usuario_ultima_visualizacao": "5818", "created_at": "2025-07-21T12:57:55.000Z", "updated_at": "2025-07-21T17:11:53.000Z", "id_situacao": "1", "caminho_pdf_final": "/home/<USER>/data/protocolo_virtual_processos/73430/2025-07-21/2a78dc13-6b7b-41fe-9386-2971a48870db.pdf", "data_arquivamento": null, "observacao_arquivamento": null, "id_usuario_arquivamento": null, "sigiloso": false, "arquivo_pdfa_final": "", "local_requisicao": "EXTERNO", "id_protocolo_pai": null, "id_processo_outro_sistema": null}], "formularios": [{"id": "198", "nome": "232323", "descricao": "teset", "nome_relatorio": "teste", "permitir_navegacao": false, "rota": "teste", "id_perfil_acesso": null, "created_at": "2022-07-14T20:10:25.000Z", "updated_at": "2022-07-14T20:10:25.000Z", "titulo": "123123123", "chave_db": "d826d0d1-764d-4278-9f94-660d84bfbee8", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": null, "posicao_logo": null}, {"id": "403", "nome": "2ª Edição das Olimpíadas Escolares", "descricao": null, "nome_relatorio": "2ª Edição das Olimpíadas Escolares", "permitir_navegacao": false, "rota": "123", "id_perfil_acesso": null, "created_at": "2025-02-06T20:21:22.000Z", "updated_at": "2025-02-06T20:21:22.000Z", "titulo": "2ª Edição das Olimpíadas Escolares do Valparaíso de Goiás - 2023", "chave_db": "3c8601bb-dc1f-4c6e-8136-2646ffb0b09a", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": null, "posicao_logo": null}, {"id": "458", "nome": "7º CORRIDA DO MEIO AMBIENTE", "descricao": null, "nome_relatorio": "Inscrição corrida", "permitir_navegacao": true, "rota": "inscrição_corrida", "id_perfil_acesso": null, "created_at": "2025-05-16T11:04:34.000Z", "updated_at": "2025-06-09T20:30:14.000Z", "titulo": null, "chave_db": "9d32abeb-6cbb-494a-894e-5ca9048684d7", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": true, "permitir_uso_em_servicos_online": true, "logo": "", "posicao_logo": null}, {"id": "283", "nome": "Abertura de Chamado TI", "descricao": "<PERSON><PERSON><PERSON>", "nome_relatorio": "<PERSON><PERSON><PERSON>", "permitir_navegacao": false, "rota": "abertura_chamado", "id_perfil_acesso": null, "created_at": "2023-09-25T18:54:11.000Z", "updated_at": "2025-01-25T13:13:48.000Z", "titulo": "Abertura de Chamado TI", "chave_db": "9e746e84-a6e6-4951-85a4-906ab5ccba98", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": "", "posicao_logo": null}, {"id": "373", "nome": "ADMISSÃO DOS COMISSIONADOS E EFETIVOS", "descricao": null, "nome_relatorio": "ADMISSÃO DOS COMISSIONADOS E EFETIVOS", "permitir_navegacao": true, "rota": "123", "id_perfil_acesso": null, "created_at": "2024-11-12T11:19:43.000Z", "updated_at": "2025-03-26T19:32:43.000Z", "titulo": "ADMISSÃO DOS COMISSIONADOS E EFETIVOS", "chave_db": "ec4bfed9-bc60-4177-bfa8-7549c04266a8", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": true, "permitir_uso_em_servicos_online": true, "logo": "", "posicao_logo": null}, {"id": "471", "nome": "ADMISSÃO DOS EFETIVOS", "descricao": null, "nome_relatorio": "ADMISSÃO DOS EFETIVOS", "permitir_navegacao": true, "rota": "admissao_efetivos", "id_perfil_acesso": null, "created_at": "2025-07-08T21:22:26.000Z", "updated_at": "2025-07-08T21:22:53.000Z", "titulo": "ADMISSÃO DOS EFETIVOS", "chave_db": "c6f1e3c1-e5e2-4557-b868-66f90a2606e5", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": "", "posicao_logo": null}, {"id": "392", "nome": "ADMISSÃO SERVIDORES CÂMARA", "descricao": null, "nome_relatorio": "ADMISSÃO DOS COMISSIONADOS", "permitir_navegacao": true, "rota": "admiss<PERSON>", "id_perfil_acesso": null, "created_at": "2025-01-25T13:16:41.000Z", "updated_at": "2025-01-25T18:25:47.000Z", "titulo": "ADMISSÃO SERVIDORES CÂMARA", "chave_db": "ef70d283-db47-47f3-b396-3eb1e874eff5", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": true, "permitir_uso_em_servicos_online": true, "logo": "", "posicao_logo": null}, {"id": "192", "nome": "Agendamento de Vistoria", "descricao": null, "nome_relatorio": "Agendamento de Vistoria", "permitir_navegacao": true, "rota": "1", "id_perfil_acesso": null, "created_at": "2022-07-06T20:33:22.000Z", "updated_at": "2022-07-06T20:33:22.000Z", "titulo": "Agendamento de Vistoria", "chave_db": "458b151a-e8d0-476d-915b-0fd8ac93a636", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": null, "posicao_logo": null}, {"id": "309", "nome": "Agendamento Laboratório", "descricao": null, "nome_relatorio": "Agendamento Laboratório", "permitir_navegacao": false, "rota": "agendamento_laboratorio", "id_perfil_acesso": null, "created_at": "2024-01-02T21:43:30.000Z", "updated_at": "2024-01-02T21:43:30.000Z", "titulo": null, "chave_db": "9eb5cda9-a2fa-41bd-aed4-52e04c92d678", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": null, "posicao_logo": null}, {"id": "400", "nome": "Agricultura", "descricao": null, "nome_relatorio": "Agricultura", "permitir_navegacao": false, "rota": "ambiental", "id_perfil_acesso": null, "created_at": "2025-01-29T21:09:37.000Z", "updated_at": "2025-02-08T19:25:07.000Z", "titulo": "Agricultura", "chave_db": "09364463-51d4-402c-ada6-88f665a17279", "versao": "*******", "modelo_padrao": false, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": true, "permitir_uso_em_servicos_online": true, "logo": "", "posicao_logo": null}, {"id": "109", "nome": "ALMOXARIFADO", "descricao": null, "nome_relatorio": "ALMOXARIFADO", "permitir_navegacao": false, "rota": "ALMOXARIFADO", "id_perfil_acesso": null, "created_at": "2022-03-14T18:04:08.000Z", "updated_at": "2022-03-16T12:54:36.000Z", "titulo": "ALMOXARIFADO", "chave_db": "47a3ac0e-6f20-43b2-b3a1-2a14f2b86792", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": null, "posicao_logo": null}, {"id": "284", "nome": "Alvará de Autorização", "descricao": "Alvará de Autorização", "nome_relatorio": "alvara-de-autorizacao", "permitir_navegacao": true, "rota": "alvara-de-autorizacao", "id_perfil_acesso": null, "created_at": "2023-10-02T18:32:50.000Z", "updated_at": "2023-10-03T20:33:56.000Z", "titulo": "Alvará de Autorização", "chave_db": "8621684a-36c1-49fc-9bd0-bdb962cf51c8", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": null, "posicao_logo": null}, {"id": "285", "nome": "Alvará de Construção", "descricao": "Processo para análise de projeto arquitetônico, e emissão do alvará de construção do respectivo projeto aprovadoProcesso para análise de projeto arquitetônico, e emissão do alvará de construção do respectivo projeto aprovado.", "nome_relatorio": "alvara-de-construcao", "permitir_navegacao": true, "rota": "alvara-de-construcao", "id_perfil_acesso": null, "created_at": "2023-10-02T20:15:33.000Z", "updated_at": "2023-10-02T20:15:33.000Z", "titulo": "Alvará de Construção", "chave_db": "db76a63b-7951-48d9-b64e-e105561f0222", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": null, "posicao_logo": null}, {"id": "286", "nome": "Alvará de Demolição", "descricao": "- Todos os arquivos devem ser assinados e escaneados.\n\n\n\nAnexe esta declaração assinada por todos os envolvidos.\nAcesse o link, vá em Arquivo, e clique em Fazer Download, e escolha (.docx)\n\nCONSULTA DAS LEGISLAÇÕES\n\nÉ importante que o requerente atente-se às Leis vigentes da União, do Estado e as Municipais seguintes:\n\n\nLei Complementar nº 067 (Código de Obras);\nLei Complementar nº 044 (Lei de parcelamento e Uso de Solo);\nLei Complementar nº 063 (Plano Diretor);\nLei Complementar nº 090 (que altera as Leis Complementar 044/2008 e 067/2012 e dá outras providências) e todas as respectivas alterações).", "nome_relatorio": "alvara-demolicao", "permitir_navegacao": true, "rota": "alvara-demolicao", "id_perfil_acesso": null, "created_at": "2023-10-03T20:20:50.000Z", "updated_at": "2023-10-03T20:20:50.000Z", "titulo": "Alvará de Demolição", "chave_db": "4058396a-8d7e-46c0-8b77-1c5ad146e834", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": null, "posicao_logo": null}, {"id": "108", "nome": "Alvará de Funcionamento", "descricao": null, "nome_relatorio": "Alvará de Funcionamento", "permitir_navegacao": true, "rota": "alvara", "id_perfil_acesso": null, "created_at": "2022-03-09T18:12:24.000Z", "updated_at": "2025-06-06T03:57:17.000Z", "titulo": "Alvará de Funcionamento", "chave_db": "b1ec3c17-f045-4321-80a9-7536b9aa84b7", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": true, "permitir_uso_em_servicos_online": true, "logo": "", "posicao_logo": null}, {"id": "61", "nome": "Alvará de Funcionamento - Pessoa Física", "descricao": null, "nome_relatorio": "Relatario", "permitir_navegacao": true, "rota": "1234", "id_perfil_acesso": null, "created_at": "2021-11-29T12:00:29.000Z", "updated_at": "2021-11-29T13:19:31.000Z", "titulo": "Alvará de Funcionamento - Pessoa Física", "chave_db": "5dd97655-016b-4e4a-876c-d6492ba30583", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": null, "posicao_logo": null}, {"id": "62", "nome": "Alvará de Funcionamento - Pessoa Física Replicado apartir de ID 61", "descricao": null, "nome_relatorio": "Relatario", "permitir_navegacao": true, "rota": "1234", "id_perfil_acesso": null, "created_at": "2021-11-29T14:17:41.000Z", "updated_at": "2021-11-29T14:17:41.000Z", "titulo": "Alvará de Funcionamento - Pessoa Física", "chave_db": "dc1c0338-5a2b-47a1-be46-e78eae43ea67", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": null, "posicao_logo": null}, {"id": "257", "nome": "Alvará de Funcionamento Provisório", "descricao": null, "nome_relatorio": "Alvará de Funcionamento", "permitir_navegacao": true, "rota": "provi", "id_perfil_acesso": null, "created_at": "2023-03-27T18:41:36.000Z", "updated_at": "2025-07-01T14:37:39.000Z", "titulo": "Alvará de Funcionamento Provisório", "chave_db": "41445c9e-174f-416a-8f08-b8a9e0a7ec7e", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": true, "permitir_uso_em_servicos_online": true, "logo": "", "posicao_logo": null}, {"id": "247", "nome": "Alvará de Funcionamento Replicado apartir de ID 108", "descricao": null, "nome_relatorio": "Alvará de Funcionamento", "permitir_navegacao": true, "rota": "teste123", "id_perfil_acesso": null, "created_at": "2023-01-29T03:23:37.000Z", "updated_at": "2023-01-29T03:23:37.000Z", "titulo": "Alvará de Funcionamento", "chave_db": "ccb51591-b6a5-4707-a62d-21b21d3edea7", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": false, "permitir_uso_em_servicos_online": true, "logo": null, "posicao_logo": null}, {"id": "191", "nome": "ALVARÁ DE LICENÇA SANITÁRIA", "descricao": null, "nome_relatorio": "ALVARÁ DE LICENÇA SANITÁRIA", "permitir_navegacao": true, "rota": "123", "id_perfil_acesso": null, "created_at": "2022-07-06T14:28:36.000Z", "updated_at": "2025-06-18T18:34:11.000Z", "titulo": "ALVARÁ DE LICENÇA SANITÁRIA", "chave_db": "b93085d8-316c-4211-b5ae-5c5027a89be8", "versao": "*******", "modelo_padrao": true, "permitir_importacao_arquivo": false, "tipo_importacao_arquivo": null, "permitir_uso_em_protocolo_eletronico": true, "permitir_uso_em_servicos_online": true, "logo": "", "posicao_logo": null}], "procedimentos": [], "estatisticas": {}}