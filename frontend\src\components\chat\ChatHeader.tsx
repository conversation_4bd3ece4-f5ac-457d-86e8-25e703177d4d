'use client';
import Image from 'next/image';
import { FaBars, FaUser } from 'react-icons/fa';
import ThemeToggle from '@/components/ThemeToggle';

interface Props {
  user?: { id: string | number; name?: string };
  onToggleSidebar: () => void;
}

export default function ChatHeader({ user, onToggleSidebar }: Props) {
  return (
    <header className="bg-white dark:bg-brand-dark border-b border-gray-200 dark:border-brand/20">
      <div className="relative flex items-center max-w-7xl mx-auto p-4">
        {/* BOTÃO SIDEBAR À ESQUERDA */}
        <div className="flex items-center">
          <button
            onClick={onToggleSidebar}
            aria-label="Alternar histórico de conversas"
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-white/10 rounded-lg transition-colors"
          >
            <FaBars />
          </button>
        </div>

        {/* LOGO PERFEITAMENTE CENTRALIZADA */}
        <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <Image
            src="/images/logo/PrefeVirt.png"
            alt="Logo Prefeitura Virtual"
            width={72}
            height={72}
            priority
            className="object-contain"
          />
        </div>

        {/* INFO USUÁRIO À DIREITA */}
        <div className="flex items-center gap-4 ml-auto">
          <ThemeToggle />
          {user && (
            <div className="flex items-center gap-2 bg-blue-50 dark:bg-blue-900/20 px-4 py-2 rounded-xl border border-blue-200 dark:border-blue-800/30">
              <FaUser className="text-blue-600 dark:text-blue-300" />
              <span className="text-xs text-blue-800 dark:text-blue-200">
                {user.name || `ID ${user.id}`}
              </span>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}