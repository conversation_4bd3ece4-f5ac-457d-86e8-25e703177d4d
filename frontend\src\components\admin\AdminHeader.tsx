'use client';
import Image from 'next/image';
import ThemeToggle from '@/components/ThemeToggle';
import { FaSignOutAlt, Fa<PERSON>ey, FaUsers } from 'react-icons/fa';

interface Props {
  activeTab: string;
  onTabChange: (tab: string) => void;
  onLogout: () => void;
}

export default function AdminHeader({ activeTab, onTabChange, onLogout }: Props) {
  return (
    <header className="bg-white dark:bg-brand-dark border-b border-gray-200 dark:border-brand/20 shadow-lg sticky top-0 z-50">
      <div className="px-6 py-4">
        <div className="relative flex items-center justify-between">
          {/* LOGO PERFEITAMENTE CENTRALIZADA */}
          <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <Image
              src="/images/logo/V.png"
              alt="Logo Prefeitura Virtual"
              width={72}
              height={72}
              priority
              className="object-contain"
            />
          </div>
          
          <div className="flex items-center space-x-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Dashboard Administrativo</h1>
              <p className="text-gray-600 dark:text-gray-300">Sistema de Monitoramento - Prefeitura Virtual</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Navigation Tabs */}
            <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              <button
                onClick={() => onTabChange('dashboard')}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  activeTab === 'dashboard'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                Dashboard
              </button>
              <button
                onClick={() => onTabChange('tokens')}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  activeTab === 'tokens'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <FaKey className="w-3 h-3 mr-1 inline" />
                Tokens
              </button>
              <button
                onClick={() => onTabChange('external-users')}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  activeTab === 'external-users'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <FaUsers className="w-3 h-3 mr-1 inline" />
                Usuários Sistema
              </button>
            </div>
            
            {/* System Status Indicator */}
            <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-lg px-3 py-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-700 dark:text-gray-300">Sistema Operacional</span>
            </div>
            
            {/* Theme Toggle */}
            <ThemeToggle />
            
            {/* Logout Button */}
            <button
              onClick={onLogout}
              className="flex items-center space-x-2 px-3 py-2 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors text-sm text-red-600 dark:text-red-400"
              title="Sair do painel admin"
            >
              <FaSignOutAlt className="w-4 h-4" />
              <span>Sair</span>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}