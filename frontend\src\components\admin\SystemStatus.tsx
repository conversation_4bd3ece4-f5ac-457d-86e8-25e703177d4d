import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui';
import { clsx } from 'clsx';
import { useSystemStatusOnly } from '@/hooks/useSystemStatus';
import { FaSpinner } from 'react-icons/fa';

interface SystemStatusProps {
  className?: string;
}

export function SystemStatus({ className }: SystemStatusProps) {
  const { 
    services, 
    summary, 
    loading, 
    error, 
    isSystemHealthy,
    refetch 
  } = useSystemStatusOnly();

  const getStatusColor = (status: 'online' | 'offline' | 'warning') => {
    switch (status) {
      case 'online':
        return 'text-green-600 bg-green-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'offline':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: 'online' | 'offline' | 'warning') => {
    switch (status) {
      case 'online':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'offline':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FaSpinner className="w-5 h-5 animate-spin text-blue-600" />
            <span>Carregando Status...</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <FaSpinner className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600">Verificando serviços...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Erro no Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <button 
              onClick={refetch}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Tentar Novamente
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Status do Sistema</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {services.map((item, index) => (
            <div key={item.name} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={clsx(
                  'flex items-center justify-center w-8 h-8 rounded-full',
                  getStatusColor(item.status)
                )}>
                  {getStatusIcon(item.status)}
                </div>
                
                <div>
                  <div className="text-sm font-medium text-gray-800">
                    {item.name}
                  </div>
                  <div className="text-xs text-gray-600">
                    {item.description}
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className={clsx(
                  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                  getStatusColor(item.status)
                )}>
                  {item.status === 'online' ? 'Online' : 
                   item.status === 'warning' ? 'Atenção' : 'Offline'}
                </div>
                {item.lastUpdate && (
                  <div className="text-xs text-gray-500 mt-1">
                    {item.lastUpdate}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {/* Overall System Health */}
        {summary && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-800">
                Saúde Geral do Sistema
              </span>
              <div className="flex items-center space-x-2">
                <div className={clsx(
                  "w-2 h-2 rounded-full",
                  summary.overallStatus === 'operational' ? 'bg-green-500 animate-pulse' :
                  summary.overallStatus === 'degraded' ? 'bg-yellow-500 animate-pulse' :
                  'bg-red-500 animate-pulse'
                )}></div>
                <span className={clsx(
                  "text-sm font-medium",
                  summary.overallStatus === 'operational' ? 'text-green-600' :
                  summary.overallStatus === 'degraded' ? 'text-yellow-600' :
                  'text-red-600'
                )}>
                  {summary.overallStatus === 'operational' ? 'Operacional' :
                   summary.overallStatus === 'degraded' ? 'Degradado' :
                   'Crítico'}
                </span>
              </div>
            </div>
            
            <div className="mt-2">
              <div className="flex space-x-1">
                <div className="flex-1 h-2 bg-gray-200 rounded-full">
                  <div 
                    className={clsx(
                      "h-2 rounded-full",
                      summary.overallStatus === 'operational' ? 'bg-green-500' :
                      summary.overallStatus === 'degraded' ? 'bg-yellow-500' :
                      'bg-red-500'
                    )}
                    style={{ width: `${summary.healthPercentage}%` }}
                  ></div>
                </div>
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>{summary.healthPercentage}% dos serviços funcionando</span>
                <span>
                  {summary.warning > 0 && `${summary.warning} alerta${summary.warning > 1 ? 's' : ''}`}
                  {summary.offline > 0 && ` ${summary.offline} offline`}
                </span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}