'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FaTimes, 
  FaPlus, 
  FaHistory, 
  FaSpinner,
  FaExclamationTriangle 
} from 'react-icons/fa';
import { useConversations, Conversation } from '@/hooks/useConversations';
import { ConversationItem } from './ConversationItem';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  activeConversationId?: string;
  onConversationSelect: (conversationId: string) => void;
  onNewConversation: () => void;
}

export function Sidebar({
  isOpen,
  onClose,
  activeConversationId,
  onConversationSelect,
  onNewConversation
}: SidebarProps) {
  const {
    conversations,
    loading,
    error,
    createConversation,
    deleteConversation,
    updateConversationTitle
  } = useConversations();

  const [isCreating, setIsCreating] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };

    checkIsDesktop();
    window.addEventListener('resize', checkIsDesktop);
    return () => window.removeEventListener('resize', checkIsDesktop);
  }, []);

  const handleNewConversation = async () => {
    try {
      setIsCreating(true);
      const newConversation = await createConversation('Nova Conversa');
      onConversationSelect(newConversation.id);
      onNewConversation();
    } catch (error) {
      console.error('Erro ao criar conversa:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteConversation = async (conversationId: string) => {
    try {
      await deleteConversation(conversationId);
      // Se a conversa deletada estava ativa, limpar seleção
      if (conversationId === activeConversationId) {
        onNewConversation();
      }
    } catch (error) {
      console.error('Erro ao deletar conversa:', error);
    }
  };

  const handleUpdateTitle = async (conversationId: string, title: string) => {
    try {
      await updateConversationTitle(conversationId, title);
    } catch (error) {
      console.error('Erro ao atualizar título:', error);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Overlay para mobile */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="fixed inset-0 bg-black/50 dark:bg-black/50 backdrop-blur-sm z-40 lg:hidden"
            onClick={onClose}
          />

          {/* Sidebar */}
          <motion.div
            initial={isDesktop ? { x: 0, opacity: 1 } : { x: -320, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={isDesktop ? { x: 0, opacity: 1 } : { x: -320, opacity: 0 }}
            transition={
              isDesktop 
                ? { 
                    duration: 0.4, 
                    ease: [0.25, 0.46, 0.45, 0.94],
                    opacity: { duration: 0.2 }
                  } 
                : { 
                    type: "spring", 
                    damping: 30, 
                    stiffness: 300,
                    opacity: { duration: 0.2 }
                  }
            }
            className="fixed lg:relative left-0 top-0 bottom-0 w-80 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-r border-gray-200 dark:border-white/10 z-50 lg:z-auto flex flex-col shadow-2xl lg:shadow-none"
          >
            {/* Header */}
            <motion.div 
              className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-white/10"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1, duration: 0.3 }}
            >
              <div className="flex items-center gap-2">
                <FaHistory className="text-blue-500 dark:text-blue-400" />
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Conversas
                </h2>
              </div>
              <button
                onClick={onClose}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-white/10"
              >
                <FaTimes />
              </button>
            </motion.div>

            {/* New Conversation Button */}
            <motion.div 
              className="p-4 border-b border-gray-200 dark:border-white/10"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              <button
                onClick={handleNewConversation}
                disabled={isCreating}
                className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-brand hover:bg-brand-600 text-white rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 active:scale-95"
              >
                {isCreating ? (
                  <FaSpinner className="animate-spin" />
                ) : (
                  <FaPlus />
                )}
                <span>Nova Conversa</span>
              </button>
            </motion.div>

            {/* Conversations List */}
            <motion.div 
              className="flex-1 overflow-y-auto p-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.4 }}
            >
              {loading && conversations.length === 0 ? (
                <div className="flex items-center justify-center py-8">
                  <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                    <FaSpinner className="animate-spin" />
                    <span>Carregando conversas...</span>
                  </div>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center py-8">
                  <div className="flex items-center gap-2 text-red-500 dark:text-red-400">
                    <FaExclamationTriangle />
                    <span className="text-sm">{error}</span>
                  </div>
                </div>
              ) : conversations.length === 0 ? (
                <div className="text-center py-8">
                  <FaHistory className="text-gray-400 dark:text-gray-600 text-4xl mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Nenhuma conversa ainda.
                  </p>
                  <p className="text-gray-500 dark:text-gray-500 text-xs mt-1">
                    Comece uma nova conversa!
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  <AnimatePresence>
                    {conversations.map((conversation) => (
                      <ConversationItem
                        key={conversation.id}
                        conversation={conversation}
                        isActive={conversation.id === activeConversationId}
                        onClick={() => {
                          onConversationSelect(conversation.id);
                        }}
                        onDelete={handleDeleteConversation}
                        onUpdateTitle={handleUpdateTitle}
                      />
                    ))}
                  </AnimatePresence>
                </div>
              )}
            </motion.div>

            {/* Footer */}
            <motion.div 
              className="p-4 border-t border-gray-200 dark:border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.3 }}
            >
              <div className="text-xs text-gray-500 dark:text-gray-500 text-center">
                {conversations.length} conversa{conversations.length !== 1 ? 's' : ''}
              </div>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}