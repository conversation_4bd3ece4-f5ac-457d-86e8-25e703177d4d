const { accessTokenService } = require('./dist/services/accessTokenService');
require('dotenv').config();

async function createTestToken() {
  try {
    console.log('🔄 Criando token de teste...');
    
    const tokenData = await accessTokenService.createAccessToken({
      description: 'Token de teste para desenvolvimento'
    });
    
    console.log('\n✅ Token criado com sucesso!');
    console.log('📋 Dados do token:');
    console.log('  Token:', tokenData.token);
    console.log('  User ID:', tokenData.userId);
    console.log('  Descrição:', tokenData.description);
    console.log('  Criado em:', tokenData.createdAt);
    
    const accessLink = accessTokenService.generateAccessLink(tokenData.token);
    console.log('\n🔗 Link de acesso:');
    console.log('  ', accessLink);
    
    console.log('\n💡 Para usar no frontend, adicione o token como parâmetro:');
    console.log('  http://localhost:3000/chat?token=' + tokenData.token);
    
    console.log('\n🧪 Para testar via API, use:');
    console.log('  curl -H "X-Access-Token: ' + tokenData.token + '" \\');
    console.log('       -H "Content-Type: application/json" \\');
    console.log('       -d \'{"message":"Olá!", "secretaria":"administracao"}\' \\');
    console.log('       http://localhost:3001/api/chat/message');
    
  } catch (error) {
    console.error('❌ Erro ao criar token:', error.message);
  }
}

createTestToken();