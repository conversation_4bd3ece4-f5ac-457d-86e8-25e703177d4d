import { Router } from 'express';
import transparencyController from '../controllers/transparencyController';

const router = Router();

/**
 * Rotas públicas para transparência municipal
 * Estas rotas NÃO requerem autenticação
 */

// Health check
router.get('/health', transparencyController.healthCheck);

// Dashboard completo
router.get('/dashboard', transparencyController.getTransparencyDashboard);

// Estatísticas
router.get('/stats/general', transparencyController.getGeneralStats);
router.get('/stats/departments', transparencyController.getDepartmentStats);

// Protocolos
router.get('/protocols/status', transparencyController.getProtocolStatusDistribution);
router.get('/protocols/volume', transparencyController.getMonthlyVolume);
router.get('/protocols/subjects', transparencyController.getPopularSubjects);
router.get('/protocol/:number', transparencyController.getPublicProtocolInfo);

export default router;