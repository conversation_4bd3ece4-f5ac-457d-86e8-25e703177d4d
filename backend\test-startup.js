#!/usr/bin/env node

/**
 * Script para testar o tempo de inicialização do backend
 * Executa o servidor e mede o tempo até ele estar pronto
 */

const { spawn } = require('child_process');
const axios = require('axios');

console.log('🔄 Testando tempo de inicialização do backend...');

const startTime = Date.now();
let serverReady = false;

// Iniciar o servidor
const serverProcess = spawn('npm', ['run', 'dev'], {
  cwd: __dirname,
  stdio: 'pipe'
});

// Monitorar logs do servidor
serverProcess.stdout.on('data', (data) => {
  const output = data.toString();
  console.log(output);
  
  // Detectar quando o servidor estiver pronto
  if (output.includes('🚀 Servidor rodando na porta')) {
    if (!serverReady) {
      serverReady = true;
      const endTime = Date.now();
      const startupTime = (endTime - startTime) / 1000;
      
      console.log(`\n🎉 SERVIDOR PRONTO EM ${startupTime.toFixed(2)} SEGUNDOS!`);
      
      // Testar health check
      setTimeout(async () => {
        try {
          const response = await axios.get('http://localhost:3001/health');
          console.log('✅ Health check passou:', response.data);
        } catch (error) {
          console.error('❌ Health check falhou:', error.message);
        }
        
        // Parar o servidor
        serverProcess.kill('SIGTERM');
        process.exit(0);
      }, 2000);
    }
  }
});

serverProcess.stderr.on('data', (data) => {
  console.error('STDERR:', data.toString());
});

// Timeout de segurança
setTimeout(() => {
  if (!serverReady) {
    console.error('❌ Timeout: Servidor não iniciou em 60 segundos');
    serverProcess.kill('SIGKILL');
    process.exit(1);
  }
}, 60000);

// Capturar sinais para cleanup
process.on('SIGINT', () => {
  console.log('\n🔄 Parando servidor...');
  serverProcess.kill('SIGTERM');
  process.exit(0);
});