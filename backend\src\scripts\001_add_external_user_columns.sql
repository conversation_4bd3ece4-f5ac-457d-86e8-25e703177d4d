-- Migration: Adici<PERSON>r colunas para integração com sistema externo da prefeitura
-- Arquivo: 001_add_external_user_columns.sql
-- Data: 2025-07-30
-- Descrição: Adiciona colunas na tabela usuarios para suportar usuários do sistema da prefeitura

-- Adicionar colunas para usuários do sistema externo
ALTER TABLE usuarios ADD COLUMN IF NOT EXISTS external_user_id VARCHAR(50);
ALTER TABLE usuarios ADD COLUMN IF NOT EXISTS secretaria VARCHAR(100);
ALTER TABLE usuarios ADD COLUMN IF NOT EXISTS cargo VARCHAR(100);
ALTER TABLE usuarios ADD COLUMN IF NOT EXISTS first_access_date TIMESTAMP;

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_usuarios_external_id ON usuarios(external_user_id);
CREATE INDEX IF NOT EXISTS idx_usuarios_secretaria ON usuarios(secretaria);

-- Comentários para documentação
COMMENT ON COLUMN usuarios.external_user_id IS 'ID do usuário no sistema da prefeitura';
COMMENT ON COLUMN usuarios.secretaria IS 'Secretaria/Departamento do usuário';
COMMENT ON COLUMN usuarios.cargo IS 'Cargo/Função do usuário';
COMMENT ON COLUMN usuarios.first_access_date IS 'Data do primeiro acesso ao chatbot';

-- Verificar se as colunas foram criadas
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'usuarios' 
AND column_name IN ('external_user_id', 'secretaria', 'cargo', 'first_access_date')
ORDER BY column_name;