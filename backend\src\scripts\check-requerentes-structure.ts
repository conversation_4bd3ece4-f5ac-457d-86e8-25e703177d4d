import { Client } from 'pg';
import { config } from 'dotenv';

config();

const postgresConfig = {
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso'
};

async function checkRequerentesStructure() {
  console.log('🔍 VERIFICANDO ESTRUTURA PARA DADOS DE REQUERENTES');
  console.log('================================================\n');
  
  const client = new Client(postgresConfig);
  
  try {
    await client.connect();
    
    // 1. Buscar tabelas que possam conter dados de requerentes
    console.log('1️⃣ Buscando tabelas relacionadas a requerentes/cidadãos...');
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND (
          table_name LIKE '%requerente%' OR
          table_name LIKE '%cidadao%' OR
          table_name LIKE '%pessoa%' OR
          table_name LIKE '%cliente%' OR
          table_name LIKE '%contribuinte%' OR
          table_name LIKE '%usuario%' OR
          table_name LIKE '%interessado%'
        )
      ORDER BY table_name
    `);
    
    console.log(`✅ Encontradas ${tablesResult.rows.length} tabelas candidatas:`);
    tablesResult.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });
    
    // 2. Verificar estrutura da tabela protocolo_virtual_processos
    console.log('\n2️⃣ Analisando tabela protocolo_virtual_processos...');
    const protocoloColumns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'protocolo_virtual_processos'
        AND (
          column_name LIKE '%requerente%' OR
          column_name LIKE '%cidadao%' OR
          column_name LIKE '%pessoa%' OR
          column_name LIKE '%nome%' OR
          column_name LIKE '%cpf%' OR
          column_name LIKE '%cnpj%'
        )
      ORDER BY ordinal_position
    `);
    
    console.log('Colunas relacionadas encontradas:');
    protocoloColumns.rows.forEach(col => {
      console.log(`   - ${col.column_name} (${col.data_type})`);
    });
    
    // 3. Verificar se existem relacionamentos via foreign keys
    console.log('\n3️⃣ Verificando relacionamentos (foreign keys)...');
    const fkResult = await client.query(`
      SELECT 
        tc.constraint_name,
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'protocolo_virtual_processos'
        AND kcu.column_name LIKE '%requerente%'
    `);
    
    if (fkResult.rows.length > 0) {
      console.log('✅ Relacionamentos encontrados:');
      fkResult.rows.forEach(fk => {
        console.log(`   - ${fk.column_name} -> ${fk.foreign_table_name}.${fk.foreign_column_name}`);
      });
    } else {
      console.log('❌ Nenhum relacionamento com tabela de requerentes encontrado');
    }
    
    // 4. Verificar dados diretamente na tabela protocolo_virtual_processos
    console.log('\n4️⃣ Verificando dados diretamente em protocolo_virtual_processos...');
    const sampleData = await client.query(`
      SELECT * FROM protocolo_virtual_processos 
      WHERE id_protocolo IS NOT NULL
      LIMIT 1
    `);
    
    if (sampleData.rows.length > 0) {
      console.log('Exemplo de registro:');
      const sample = sampleData.rows[0];
      Object.keys(sample).forEach(key => {
        if (key.includes('nome') || key.includes('cpf') || key.includes('cnpj') || 
            key.includes('requerente') || key.includes('cidadao') || key.includes('pessoa')) {
          console.log(`   - ${key}: ${sample[key] || 'NULL'}`);
        }
      });
    }
    
    // 5. Buscar todas as colunas da tabela para análise completa
    console.log('\n5️⃣ Estrutura completa da tabela protocolo_virtual_processos...');
    const allColumns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'protocolo_virtual_processos'
      ORDER BY ordinal_position
    `);
    
    console.log('Todas as colunas:');
    allColumns.rows.forEach(col => {
      console.log(`   - ${col.column_name} (${col.data_type})`);
    });
    
    // 6. Verificar se os dados estão em formato JSON ou campos separados
    console.log('\n6️⃣ Verificando se há campos JSON com dados de requerentes...');
    const jsonColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'protocolo_virtual_processos'
        AND (data_type = 'json' OR data_type = 'jsonb')
    `);
    
    if (jsonColumns.rows.length > 0) {
      console.log('✅ Campos JSON encontrados:');
      for (const col of jsonColumns.rows) {
        console.log(`   - ${col.column_name}`);
        
        // Verificar conteúdo do JSON
        const jsonSample = await client.query(`
          SELECT ${col.column_name} 
          FROM protocolo_virtual_processos 
          WHERE ${col.column_name} IS NOT NULL
          LIMIT 1
        `);
        
        if (jsonSample.rows.length > 0) {
          console.log(`     Exemplo de conteúdo:`, JSON.stringify(jsonSample.rows[0][col.column_name], null, 2));
        }
      }
    }
    
  } catch (error: any) {
    console.error('❌ Erro:', error.message);
  } finally {
    await client.end();
  }
  
  console.log('\n✅ Análise concluída!');
}

// Executar análise
checkRequerentesStructure().catch(console.error);