'use client';
import { useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>aR<PERSON>ot, <PERSON>a<PERSON><PERSON>, FaCoins, FaCopy, FaCheckCircle } from 'react-icons/fa';
import Image from 'next/image';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  metadata?: {
    cost?: number;
    tokensUsed?: number;
  };
}

interface Props {
  messages: Message[];
  isLoading: boolean;
  onQuickAction?: (question: string) => void;
  copiedMessageId?: string | null;
  onCopyMessage?: (content: string, id: string) => void;
}

export default function MessagesArea({ 
  messages, 
  isLoading, 
  onQuickAction,
  copiedMessageId,
  onCopyMessage 
}: Props) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const quickActions = [
    "Qual o último protocolo registrado?",
    "Quais serviços estão disponíveis?",
    "Como consultar um alvará?",
    "Informações sobre departamentos"
  ];

  return (
    <div className="flex-1 overflow-hidden bg-gray-50 dark:bg-brand-dark">
      <div className="h-full max-w-4xl mx-auto px-4 py-6 overflow-y-auto">
        {messages.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              {/* Logo */}
              <div className="relative w-32 h-32 mx-auto mb-6">
                <div className="absolute inset-0 bg-brand/20 rounded-xl blur-2xl" />
                <div className="relative rounded-xl p-6">
                  <Image
                    src="/images/logo/V.png"
                    alt="Prefeitura Virtual"
                    width={80}
                    height={80}
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>

              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Olá! Como posso ajudar?
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-8">
                Faça perguntas sobre serviços, protocolos e informações municipais
              </p>
              
              {/* Quick Actions */}
              {onQuickAction && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-md mx-auto">
                  {quickActions.map((question, index) => (
                    <button
                      key={index}
                      onClick={() => onQuickAction(question)}
                      className="p-3 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 border border-blue-200 dark:border-blue-800/30 rounded-xl text-sm text-blue-800 dark:text-blue-200 transition-all hover:scale-105 shadow-sm hover:shadow-md"
                    >
                      {question}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`flex max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'} gap-3`}>
                  {/* Avatar */}
                  <div
                    className={`w-10 h-10 rounded-xl flex items-center justify-center flex-shrink-0 ${
                      message.type === 'user' 
                        ? 'bg-brand' 
                        : 'bg-transparent'
                    }`}
                  >
                    {message.type === 'user' ? (
                      <FaUser className="text-white" />
                    ) : (
                      <Image
                        src="/images/logo/V.png"
                        alt="Bot Avatar"
                        width={40}
                        height={40}
                        className="object-contain"
                      />
                    )}
                  </div>

                  {/* Message Content */}
                  <div className={`flex flex-col ${message.type === 'user' ? 'items-end' : 'items-start'}`}>
                    <div
                      className={`relative group px-4 py-3 rounded-2xl ${
                        message.type === 'user'
                          ? 'bg-brand text-white'
                          : 'bg-gray-100 dark:bg-white/10 backdrop-blur-lg border border-gray-200 dark:border-white/20 text-gray-900 dark:text-white'
                      }`}
                    >
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      
                      {/* Copy button */}
                      {onCopyMessage && (
                        <button
                          onClick={() => onCopyMessage(message.content, message.id)}
                          aria-label="Copiar mensagem"
                          className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-black/50 p-1.5 rounded-lg"
                        >
                          {copiedMessageId === message.id ? (
                            <FaCheckCircle className="text-green-400 text-xs" />
                          ) : (
                            <FaCopy className="text-gray-400 text-xs hover:text-white" />
                          )}
                        </button>
                      )}
                    </div>

                    {/* Metadata */}
                    <div className="flex items-center gap-2 mt-1 text-xs text-gray-500 dark:text-gray-500">
                      <FaClock className="text-gray-400 dark:text-gray-600" />
                      <span>
                        {new Date(message.timestamp).toLocaleTimeString('pt-BR', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                      {message.metadata?.cost && (
                        <>
                          <span>•</span>
                          <FaCoins className="text-yellow-600" />
                          <span>${message.metadata.cost.toFixed(4)}</span>
                        </>
                      )}
                      {message.metadata?.tokensUsed && (
                        <>
                          <span>•</span>
                          <span>{message.metadata.tokensUsed} tokens</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Typing Indicator */}
            {isLoading && (
              <div className="flex justify-start">
                <div className="flex max-w-[80%] gap-3">
                  <div className="w-10 h-10 rounded-xl bg-transparent flex items-center justify-center flex-shrink-0">
                    <Image
                      src="/images/logo/V.png"
                      alt="Bot Avatar"
                      width={40}
                      height={40}
                      className="object-contain"
                    />
                  </div>
                  <div className="bg-gray-100 dark:bg-white/10 backdrop-blur-lg border border-gray-200 dark:border-white/20 px-4 py-3 rounded-2xl">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0s' }} />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }} />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Scroll anchor */}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
    </div>
  );
}