'use client';

import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { 
  FaRobot, 
  FaShieldAlt, 
  FaBolt, 
  FaChartLine,
  FaLock,
  FaUsers,
  FaDatabase,
  FaCogs,
  FaArrowRight,
  FaCheckCircle,
  FaBuilding,
  FaFileAlt,
  FaClock,
  FaEye
} from 'react-icons/fa';
import { useState, useEffect } from 'react';
// import { useTransparency } from '../hooks/useTransparency';
// import { StatsCard } from '../components/transparency/StatsCard';
// import { ProtocolSearch } from '../components/transparency/ProtocolSearch';

export default function HomePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  // const { dashboard, loading, error, lastUpdate, consultarProtocolo } = useTransparency();
  const [stats, setStats] = useState({
    totalServidores: 2,
    totalDepartamentos: 458,
    totalProtocolos: 5645,
    tempoMedioTramitacao: 15
  });

  const handleAccessChat = () => {
    setIsLoading(true);
    // Verificar se há token na URL ou localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token') || localStorage.getItem('chatbot_access_token');
    
    if (token) {
      router.push(`/chat?token=${token}`);
    } else {
      alert('Você precisa de um token de acesso autorizado!');
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-800 via-slate-700 to-slate-900 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-emerald-600 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-blob" />
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-blue-600 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-blob animation-delay-2000" />
        <div className="absolute top-40 left-40 w-96 h-96 bg-amber-600 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-blob animation-delay-4000" />
      </div>

      {/* Header */}
      <motion.header
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="relative z-10 bg-black/10 backdrop-blur-lg border-b border-white/10"
      >
        <div className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.div
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
              className="w-12 h-12 bg-gradient-to-br from-emerald-600 to-blue-700 rounded-xl flex items-center justify-center"
            >
              <FaBuilding className="text-white text-2xl" />
            </motion.div>
            <div>
              <h1 className="text-xl font-bold text-white">Portal da Transparência</h1>
              <p className="text-sm text-gray-400">Prefeitura Municipal de Valparaíso de Goiás</p>
            </div>
          </div>

          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.3 }}
            className="flex items-center space-x-2 bg-green-500/20 px-4 py-2 rounded-full"
          >
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-sm text-green-400">Sistema Operacional</span>
          </motion.div>
        </div>
      </motion.header>

      {/* Hero Section */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 py-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <motion.h1
            initial={{ scale: 0.5 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", duration: 0.8 }}
            className="text-5xl md:text-6xl font-bold text-white mb-6"
          >
            Portal de <span className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 to-blue-500">Transparência</span>
          </motion.h1>
          
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto"
          >
            Consulte dados públicos municipais, acompanhe protocolos e acesse informações 
            da administração pública em conformidade com a Lei de Acesso à Informação.
          </motion.p>

          <motion.button
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleAccessChat}
            disabled={isLoading}
            className="group relative px-8 py-4 bg-gradient-to-r from-emerald-600 to-blue-700 rounded-2xl text-white font-semibold text-lg transition-all hover:shadow-2xl hover:shadow-emerald-500/25 disabled:opacity-50"
          >
            <span className="flex items-center gap-3">
              {isLoading ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                  />
                  Acessando...
                </>
              ) : (
                <>
                  <FaLock />
                  Acessar Sistema
                  <FaArrowRight className="group-hover:translate-x-1 transition-transform" />
                </>
              )}
            </span>
          </motion.button>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-16"
        >
          {[
            { 
              icon: FaUsers, 
              label: "Servidores Ativos", 
              value: stats.totalServidores.toLocaleString('pt-BR'),
              subtitle: "Funcionários públicos",
              color: "from-blue-600 to-blue-700"
            },
            { 
              icon: FaBuilding, 
              label: "Departamentos", 
              value: stats.totalDepartamentos.toLocaleString('pt-BR'),
              subtitle: "Secretarias municipais",
              color: "from-emerald-600 to-emerald-700"
            },
            { 
              icon: FaFileAlt, 
              label: "Protocolos (Ano)", 
              value: stats.totalProtocolos.toLocaleString('pt-BR'),
              subtitle: "Processos registrados",
              color: "from-slate-600 to-slate-700"
            },
            { 
              icon: FaClock, 
              label: "Tempo Médio", 
              value: `${stats.tempoMedioTramitacao} dias`,
              subtitle: "Tramitação de processos",
              color: "from-amber-600 to-amber-700"
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.8 + index * 0.1 }}
              whileHover={{ y: -5, scale: 1.02 }}
              className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-6"
            >
              <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center mb-4`}>
                <stat.icon className="text-white text-xl" />
              </div>
              <p className="text-gray-400 text-sm mb-1">{stat.label}</p>
              <p className="text-2xl font-bold text-white">{stat.value}</p>
              <p className="text-gray-500 text-xs">{stat.subtitle}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* Protocol Search Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
          className="mb-16"
        >
          <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-white mb-2">
                Consultar Protocolo
              </h3>
              <p className="text-gray-400">
                Digite o número do protocolo para consultar o status público
              </p>
            </div>
            <div className="text-center text-gray-500">
              <p>📋 Sistema em implementação</p>
              <p className="text-sm mt-2">Consulta de protocolos será disponibilizada em breve</p>
            </div>
          </div>
        </motion.div>

        {/* Transparency Features */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.4 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {[
            {
              icon: FaEye,
              title: "Transparência Ativa",
              description: "Dados municipais atualizados em tempo real, disponíveis para consulta pública.",
              features: ["Dados em tempo real", "Consulta pública", "Lei de Acesso à Informação"]
            },
            {
              icon: FaFileAlt,
              title: "Consulta de Protocolos",
              description: "Acompanhe o status dos protocolos públicos sem necessidade de cadastro.",
              features: ["Status atualizado", "Histórico público", "Sem dados pessoais"]
            },
            {
              icon: FaChartLine,
              title: "Indicadores de Gestão",
              description: "Métricas de performance e eficiência da administração pública municipal.",
              features: ["Performance departamental", "Tempo de tramitação", "Volume de processos"]
            }
          ].map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.5 + index * 0.1 }}
              whileHover={{ y: -10 }}
              className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 group"
            >
              <div className="w-16 h-16 bg-gradient-to-br from-slate-600 to-slate-700 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <feature.icon className="text-white text-2xl" />
              </div>
              
              <h3 className="text-xl font-bold text-white mb-3">{feature.title}</h3>
              <p className="text-gray-400 mb-6">{feature.description}</p>
              
              <ul className="space-y-2">
                {feature.features.map((item, i) => (
                  <li key={i} className="flex items-center gap-2 text-sm text-gray-300">
                    <FaCheckCircle className="text-green-500 flex-shrink-0" />
                    {item}
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </motion.div>

        {/* Quick Access for Authorized Users */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.8 }}
          className="mt-16 text-center"
        >
          <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8">
            <h3 className="text-xl font-bold text-white mb-4">
              Acesso para Usuários Autorizados
            </h3>
            <p className="text-gray-400 mb-6">
              Servidores municipais podem acessar o sistema completo com token de autorização
            </p>
            <motion.button
              onClick={handleAccessChat}
              disabled={isLoading}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-3 bg-gradient-to-r from-emerald-600 to-blue-700 rounded-xl text-white font-semibold transition-all hover:shadow-lg disabled:opacity-50"
            >
              <span className="flex items-center gap-2">
                <FaLock />
                {isLoading ? 'Verificando...' : 'Acessar Sistema Restrito'}
              </span>
            </motion.button>
          </div>
        </motion.div>
      </div>

      {/* Footer */}
      <motion.footer
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.5 }}
        className="relative z-10 bg-black/20 backdrop-blur-lg border-t border-white/10 mt-20"
      >
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="text-center md:text-left">
              <p className="text-white font-semibold mb-1">
                Portal de Transparência - Prefeitura de Valparaíso de Goiás
              </p>
              <p className="text-gray-400 text-sm">
                © 2025 - Dados atualizados em tempo real
              </p>
              <p className="text-gray-500 text-xs mt-1">
                Última atualização: {new Date().toLocaleString('pt-BR')}
              </p>
            </div>
            
            <div className="flex items-center gap-6">
              <div className="text-center">
                <p className="text-gray-400 text-sm">Transparência</p>
                <p className="text-white font-semibold">Lei de Acesso</p>
              </div>
              <div className="w-px h-8 bg-white/20" />
              <div className="text-center">
                <p className="text-gray-400 text-sm">Sistema</p>
                <p className="text-green-400 font-semibold flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  Online
                </p>
              </div>
            </div>
          </div>
        </div>
      </motion.footer>

      <style jsx>{`
        @keyframes blob {
          0% { transform: translate(0px, 0px) scale(1); }
          33% { transform: translate(30px, -50px) scale(1.1); }
          66% { transform: translate(-20px, 20px) scale(0.9); }
          100% { transform: translate(0px, 0px) scale(1); }
        }
        
        .animate-blob {
          animation: blob 7s infinite;
        }
        
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>
    </div>
  );
}