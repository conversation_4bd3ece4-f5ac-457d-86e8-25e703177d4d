import { useState, useEffect, useCallback } from 'react';

interface ServiceStatus {
  name: string;
  status: 'online' | 'offline' | 'warning';
  description: string;
  lastUpdate: string;
  latency?: number;
  details?: any;
}

interface SystemStatusData {
  services: ServiceStatus[];
  summary: {
    total: number;
    online: number;
    warning: number;
    offline: number;
    healthPercentage: number;
    overallStatus: 'operational' | 'degraded' | 'critical';
  };
  timestamp: string;
}

interface LogEntry {
  id: string;
  timestamp: Date;
  level: 'info' | 'warning' | 'error' | 'success';
  message: string;
  source: string;
  details?: any;
}

interface SystemLogsData {
  logs: LogEntry[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export function useSystemStatus() {
  const [statusData, setStatusData] = useState<SystemStatusData | null>(null);
  const [logsData, setLogsData] = useState<SystemLogsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const API_BASE = process.env.NODE_ENV === 'production' 
    ? '/api' 
    : 'http://localhost:3001/api';

  /**
   * Buscar status do sistema
   */
  const fetchSystemStatus = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE}/system/status`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'X-Access-Token': localStorage.getItem('access_token') || ''
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao buscar status do sistema');
      }

      const data = await response.json();
      
      if (data.success) {
        setStatusData(data.data);
      } else {
        throw new Error(data.error || 'Erro desconhecido');
      }
    } catch (err: any) {
      console.error('Erro ao buscar status do sistema:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [API_BASE]);

  /**
   * Buscar logs do sistema
   */
  const fetchSystemLogs = useCallback(async (options?: {
    level?: string;
    limit?: number;
    offset?: number;
  }) => {
    const { level = 'all', limit = 50, offset = 0 } = options || {};
    
    try {
      const params = new URLSearchParams({
        level,
        limit: limit.toString(),
        offset: offset.toString()
      });

      const response = await fetch(`${API_BASE}/system/logs?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'X-Access-Token': localStorage.getItem('access_token') || ''
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao buscar logs do sistema');
      }

      const data = await response.json();
      
      if (data.success) {
        setLogsData(data.data);
      } else {
        throw new Error(data.error || 'Erro desconhecido');
      }
    } catch (err: any) {
      console.error('Erro ao buscar logs do sistema:', err);
      setError(err.message);
    }
  }, [API_BASE]);

  // Buscar dados na montagem do componente
  useEffect(() => {
    fetchSystemStatus();
    fetchSystemLogs();
  }, [fetchSystemStatus, fetchSystemLogs]);

  // Atualizar dados periodicamente
  useEffect(() => {
    const interval = setInterval(() => {
      fetchSystemStatus();
    }, 30000); // Atualizar a cada 30 segundos

    return () => clearInterval(interval);
  }, [fetchSystemStatus]);

  return {
    statusData,
    logsData,
    loading,
    error,
    refetchStatus: fetchSystemStatus,
    refetchLogs: fetchSystemLogs,
    
    // Dados formatados para facilitar uso
    services: statusData?.services || [],
    summary: statusData?.summary,
    logs: logsData?.logs || [],
    
    // Status helpers
    isSystemHealthy: statusData?.summary.overallStatus === 'operational',
    criticalServices: statusData?.services.filter(s => s.status === 'offline') || [],
    warningServices: statusData?.services.filter(s => s.status === 'warning') || []
  };
}

/**
 * Hook específico apenas para status
 */
export function useSystemStatusOnly() {
  const { 
    statusData, 
    loading, 
    error, 
    refetchStatus, 
    services, 
    summary,
    isSystemHealthy,
    criticalServices,
    warningServices 
  } = useSystemStatus();

  return {
    statusData,
    loading,
    error,
    refetch: refetchStatus,
    services,
    summary,
    isSystemHealthy,
    criticalServices,
    warningServices
  };
}

/**
 * Hook específico apenas para logs
 */
export function useSystemLogs() {
  const { 
    logsData, 
    loading, 
    error, 
    refetchLogs, 
    logs 
  } = useSystemStatus();

  return {
    logsData,
    loading,
    error,
    refetch: refetchLogs,
    logs,
    loadMore: (offset: number) => refetchLogs({ offset })
  };
}