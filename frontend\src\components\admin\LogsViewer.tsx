import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui';
import { clsx } from 'clsx';
import { NoSSR } from '@/components/ui/NoSSR';
import { useSystemLogs } from '@/hooks/useSystemStatus';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaSync, FaFilter } from 'react-icons/fa';

interface LogsViewerProps {
  className?: string;
}

export function LogsViewer({ className }: LogsViewerProps) {
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  const { logs, loading, error, refetch } = useSystemLogs();
  
  // Filtrar logs por nível se não for 'all'
  const filteredLogs = selectedLevel === 'all' 
    ? logs 
    : logs.filter(log => log.level === selectedLevel);

  const getLevelColor = (level: 'info' | 'warning' | 'error' | 'success') => {
    switch (level) {
      case 'success':
        return 'text-green-700 bg-green-100 border-green-200';
      case 'info':
        return 'text-blue-700 bg-blue-100 border-blue-200';
      case 'warning':
        return 'text-yellow-700 bg-yellow-100 border-yellow-200';
      case 'error':
        return 'text-red-700 bg-red-100 border-red-200';
      default:
        return 'text-gray-700 bg-gray-100 border-gray-200';
    }
  };

  const getLevelIcon = (level: 'info' | 'warning' | 'error' | 'success') => {
    switch (level) {
      case 'success':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'info':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Date(timestamp).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FaSpinner className="w-5 h-5 animate-spin text-blue-600" />
            <span>Carregando Logs...</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <FaSpinner className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600">Carregando logs do sistema...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Erro nos Logs</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <button 
              onClick={() => refetch()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Tentar Novamente
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <NoSSR>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>Logs do Sistema</span>
              <span className="text-sm text-gray-500">({filteredLogs.length})</span>
            </CardTitle>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => refetch()}
                className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="Atualizar logs"
              >
                <FaSync className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          {/* Level Filter */}
          <div className="flex items-center space-x-2 mt-4">
            <FaFilter className="w-4 h-4 text-gray-500" />
            <select
              value={selectedLevel}
              onChange={(e) => setSelectedLevel(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Todos os níveis</option>
              <option value="success">Sucesso</option>
              <option value="info">Informação</option>
              <option value="warning">Aviso</option>
              <option value="error">Erro</option>
            </select>
          </div>
        </CardHeader>

        <CardContent>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {filteredLogs.length === 0 ? (
              <div className="text-center py-8">
                <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p className="text-gray-600">
                  {selectedLevel === 'all' ? 'Nenhum log encontrado' : `Nenhum log de nível "${selectedLevel}" encontrado`}
                </p>
              </div>
            ) : (
              filteredLogs.map((log) => (
                <div key={log.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className={clsx(
                        'flex items-center justify-center w-8 h-8 rounded-full border',
                        getLevelColor(log.level)
                      )}>
                        {getLevelIcon(log.level)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className={clsx(
                            'inline-flex px-2 py-1 text-xs font-medium rounded-full border',
                            getLevelColor(log.level)
                          )}>
                            {log.level.toUpperCase()}
                          </span>
                          <span className="text-xs text-gray-500 font-medium">
                            {log.source}
                          </span>
                        </div>
                        
                        <p className="text-sm text-gray-900 mb-2">
                          {log.message}
                        </p>
                        
                        {log.details && Object.keys(log.details).length > 0 && (
                          <div className="text-xs text-gray-600 bg-gray-100 rounded p-2 font-mono">
                            <pre className="whitespace-pre-wrap">
                              {JSON.stringify(log.details, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="text-xs text-gray-500 ml-4 flex-shrink-0">
                      {formatTimestamp(log.timestamp)}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
          
          {filteredLogs.length > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex justify-between items-center text-xs text-gray-500">
                <span>Mostrando {filteredLogs.length} logs</span>
                <span>Atualizado automaticamente</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </NoSSR>
  );
}