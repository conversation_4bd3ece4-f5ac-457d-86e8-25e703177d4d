/**
 * Script para executar migration de usuários externos
 * Adiciona colunas necessárias para integração com sistema da prefeitura
 */

import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';

const pool = new Pool({
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 10000,
});

async function executeMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Iniciando migration para usuários externos...');
    
    // Ler arquivo SQL da migration
    const migrationPath = path.join(__dirname, '001_add_external_user_columns.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Executar migration
    console.log('📝 Executando comandos SQL...');
    await client.query(migrationSQL);
    
    console.log('✅ Migration executada com sucesso!');
    console.log('📊 Verificando estrutura da tabela...');
    
    // Verificar se as colunas foram criadas
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'usuarios' 
      AND column_name IN ('external_user_id', 'secretaria', 'cargo', 'first_access_date')
      ORDER BY column_name
    `);
    
    console.log('\n📋 Colunas adicionadas:');
    result.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    
    // Verificar índices criados
    const indexResult = await client.query(`
      SELECT indexname, indexdef 
      FROM pg_indexes 
      WHERE tablename = 'usuarios' 
      AND indexname LIKE '%external%' OR indexname LIKE '%secretaria%'
    `);
    
    console.log('\n🔍 Índices criados:');
    indexResult.rows.forEach(row => {
      console.log(`  - ${row.indexname}`);
    });
    
    console.log('\n🎉 Migration completada com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro ao executar migration:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar migration
executeMigration()
  .then(() => {
    console.log('✨ Script finalizado.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Falha na migration:', error);
    process.exit(1);
  });