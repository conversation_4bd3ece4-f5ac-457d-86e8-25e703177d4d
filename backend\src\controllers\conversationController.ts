import { Request, Response } from 'express';
import { CachedRequest } from '../middleware/cacheMiddleware';
import { postgresRepository } from '../repositories/PostgreSQLRepository';

class ConversationController {
  // Salvar mensagem na conversa (usado internamente pelo ChatController)
  static async saveMessage(
    conversationId: string,
    userId: string | number,
    content: string,
    role: 'user' | 'assistant',
    metadata?: {
      responseTime?: number;
      tokensUsed?: number;
      cost?: number;
    }
  ): Promise<void> {
    try {
      // Converter userId para número se necessário
      const numericUserId = typeof userId === 'string' ? parseInt(userId) : userId;
      
      if (isNaN(numericUserId)) {
        console.warn(`⚠️ UserId inválido para salvar mensagem: "${userId}" - pulando salvamento`);
        return; // Não falhar, apenas não salvar
      }

      // Verificar se o usuário tem acesso à conversa
      const user = await postgresRepository.findUserById(numericUserId.toString());
      if (!user) {
        console.warn(`⚠️ Usuário ${numericUserId} não encontrado - pulando salvamento de mensagem`);
        return; // Não falhar, apenas não salvar
      }

      // Salvar mensagem no PostgreSQL
      await postgresRepository.addMessage({
        conversationId,
        content,
        role,
        tokensUsed: metadata?.tokensUsed,
        cost: metadata?.cost,
        cacheHit: false,
        processingTime: metadata?.responseTime,
        metadata: metadata || {}
      });

      console.log(`💾 Mensagem salva na conversa ${conversationId}`);
    } catch (error) {
      console.error('Erro ao salvar mensagem:', error);
      throw error;
    }
  }

  // Criar conversa se não existir
  static async ensureConversation(userId: string | number): Promise<string> {
    try {
      // Converter userId para número se necessário
      const numericUserId = typeof userId === 'string' ? parseInt(userId) : userId;
      
      if (isNaN(numericUserId)) {
        throw new Error(`UserId inválido: "${userId}" - deve ser um número`);
      }

      const user = await postgresRepository.findUserById(numericUserId.toString());
      if (!user) {
        throw new Error('Usuário não encontrado');
      }

      // Buscar ou criar conversa ativa
      const conversation = await postgresRepository.getOrCreateActiveConversation(user.id);
      return conversation.id;
    } catch (error) {
      console.error('Erro ao garantir conversa:', error);
      throw error;
    }
  }
}

export default ConversationController;