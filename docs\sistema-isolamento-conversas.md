# Sistema de Isolamento de Conversas por Token

## 📋 Visão Geral

O sistema agora suporta isolamento completo de conversas através de tokens de acesso permanentes. Cada usuário autorizado pelo Prefeito recebe um link único que permite acesso individualizado ao chatbot, com suas conversas salvas de forma permanente e isolada.

## 🔐 Como Funciona

### 1. Token Permanente
- Cada link autorizado contém um token único permanente
- O token identifica um "usuário anônimo" específico no banco de dados
- As conversas ficam sempre associadas ao mesmo usuário, independentemente de quando acessar

### 2. Autenticação Automática
- Frontend captura token da URL automaticamente
- Salva no localStorage para acesso posterior
- Remove token da URL para segurança
- Valida token no backend automaticamente

### 3. Isolamento Total
- Cada token mapeia para um usuário único no PostgreSQL
- Conversas são completamente isoladas por usuário
- Sidebar mostra apenas conversas do usuário específico
- Nenhum usuário consegue ver conversas de outros

## 🛠️ Administração de Tokens

### Criar Novo Token
```bash
cd backend
npm run create-token "Secretário de Saúde - João Silva"
```

**Output:**
```
✅ Token de acesso criado com sucesso!

📋 INFORMAÇÕES DO TOKEN:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🆔 User ID: 123
📝 Descrição: Secretário de Saúde - João Silva
📅 Criado em: 28/07/2025 14:30:00
🔑 Token: abc123def456...
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🔗 LINK DE ACESSO:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
http://localhost:3000/chat?token=abc123def456...
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

### Listar Tokens Ativos
```bash
cd backend
npm run list-tokens
```

### Revogar Token
```bash
cd backend
npm run revoke-token abc123def456...
```

## 🔄 Fluxo do Usuário

### 1. Primeiro Acesso
1. Usuário recebe link: `http://localhost:3000/chat?token=abc123...`
2. Abre o link no navegador
3. Frontend captura token automaticamente
4. Salva token no localStorage
5. Remove token da URL
6. Autentica no backend automaticamente
7. Cria primeira conversa

### 2. Acessos Posteriores
1. Usuário acessa: `http://localhost:3000/chat`
2. Frontend verifica localStorage
3. Encontra token salvo
4. Autentica automaticamente
5. Carrega todas as conversas do usuário
6. Usuário continua de onde parou

### 3. Comportamento de Segurança
- Se token inválido: Tela de "Acesso Negado"
- Se token expirado: Solicita novo link autorizado
- Dados ficam isolados por usuário sempre

## 🏗️ Arquitetura Técnica

### Backend
```
📁 services/accessTokenService.ts    # Gerenciamento de tokens
📁 middleware/tokenAuthMiddleware.ts # Autenticação flexível
📁 routes/tokenRoutes.ts            # APIs de validação
📁 repositories/PostgreSQLRepository.ts # Usuários anônimos
```

### Frontend
```
📁 hooks/useTokenAuth.ts            # Hook de autenticação
📁 services/api.ts                  # Interceptor de tokens
📁 app/chat/page.tsx               # Interface protegida
```

### Banco de Dados
```sql
-- Novas colunas na tabela usuarios
ALTER TABLE usuarios ADD COLUMN access_token VARCHAR(255) UNIQUE;
ALTER TABLE usuarios ADD COLUMN description TEXT;
ALTER TABLE usuarios ADD COLUMN is_anonymous BOOLEAN DEFAULT false;
```

## 🔒 Segurança

### Características de Segurança
- **Tokens únicos**: 256 bits de entropia (URL-safe base64)
- **Validação dupla**: Frontend + Backend
- **Isolamento completo**: Conversas separadas por usuário
- **Sem vazamento**: Token removido da URL após uso
- **Revogação**: Admin pode revogar tokens a qualquer momento

### Boas Práticas
- Tokens nunca aparecem em logs
- URLs limpas após autenticação
- Dados sensíveis mascarados
- Validação em todas as requisições

## 📊 Monitoramento

### Logs de Acesso
```bash
# Ver tentativas de autenticação
tail -f backend/logs/security-$(date +%Y-%m-%d).log

# Verificar tokens ativos
npm run list-tokens
```

### Métricas de Uso
- Tokens criados por dia
- Conversas por usuário
- Acessos por token
- Tentativas de acesso inválido

## 🚀 Exemplo de Uso Completo

### Passo 1: Admin Cria Token
```bash
cd backend
npm run create-token "Diretor de Obras - Maria Santos"
# Copia link gerado: http://localhost:3000/chat?token=xyz789...
```

### Passo 2: Prefeito Envia Link
- Envia link por email, WhatsApp ou outro meio seguro
- Usuário acessa o link no navegador

### Passo 3: Usuário Usa Chatbot
- Link abre chatbot automaticamente autenticado
- Usuário faz perguntas e conversa normalmente
- Conversas ficam salvas no sidebar

### Passo 4: Acessos Futuros
- Usuário acessa `http://localhost:3000/chat` (sem token)
- Sistema reconhece automaticamente
- Todas as conversas anteriores aparecem
- Continua conversando normalmente

### Passo 5: Admin Monitora (Opcional)
```bash
npm run list-tokens  # Ver todos os tokens ativos
# Pode revogar se necessário
```

## ⚡ Vantagens da Solução

✅ **Sem Login/Senha**: UX simples para usuário final  
✅ **Isolamento Total**: Cada usuário vê apenas suas conversas  
✅ **Persistência**: Conversas nunca se perdem  
✅ **Controle Admin**: Prefeito controla acesso via tokens  
✅ **Segurança**: Tokens únicos e revogáveis  
✅ **Escalável**: Suporta muitos usuários simultâneos  
✅ **Auditável**: Logs completos de acesso  
✅ **Flexível**: Funciona com ou sem token JWT tradicional  

## 🔍 Solução de Problemas

### "Acesso Negado"
- **Causa**: Token inválido, expirado ou revogado
- **Solução**: Solicitar novo link ao administrador

### "Conversa não carrega"
- **Causa**: Problema de conectividade ou token
- **Solução**: Recarregar página ou verificar conexão

### "Token não funciona"
- **Causa**: Token pode ter sido revogado
- **Solução**: Admin verificar com `npm run list-tokens`

---

## 📞 Suporte

Para problemas ou dúvidas sobre o sistema de isolamento:

1. Verificar logs: `backend/logs/`
2. Testar token: `npm run list-tokens`
3. Recriar token se necessário: `npm run create-token`
4. Verificar documentação: `docs/`

**Status**: ✅ Sistema implementado e funcional  
**Versão**: 1.0.0  
**Data**: 28/07/2025