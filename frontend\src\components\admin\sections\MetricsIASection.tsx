'use client';

import { 
  Accordion, 
  Accordion<PERSON>ontent, 
  Accordion<PERSON><PERSON>, 
  Accordion<PERSON><PERSON>ger,
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui';
import { MetricCard } from '@/components/admin';
import { FaRobot, FaDollarSign, FaTachometerAlt, FaDatabase } from 'react-icons/fa';

interface MetricsIASectionProps {
  metrics?: any;
  isLoading?: boolean;
}

export function MetricsIASection({ metrics, isLoading }: MetricsIASectionProps) {
  if (isLoading) {
    return (
      <div className="w-full max-w-none space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
        <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6 gap-2 sm:gap-3 lg:gap-4 xl:gap-2 2xl:gap-3">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-none space-y-6">
      {/* Header */}
      <div className="w-full flex items-center space-x-3">
        <FaRobot className="w-8 h-8 text-blue-600 dark:text-blue-400" />
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Métricas de Inteligência Artificial
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitoramento de custos, performance e utilização da IA
          </p>
        </div>
      </div>

      {/* Accordion com métricas organizadas */}
      <Accordion type="multiple" defaultValue={["custos", "performance"]} className="w-full space-y-4">
        {/* Seção de Custos */}
        <AccordionItem value="custos">
          <AccordionTrigger className="text-lg font-semibold">
            <div className="flex items-center space-x-2">
              <FaDollarSign className="w-5 h-5 text-yellow-600" />
              <span>Custos e Orçamento</span>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-3 gap-2 sm:gap-3 lg:gap-4 xl:gap-2 2xl:gap-3 pt-4">
              <MetricCard
                title="Custo Hoje"
                value={metrics?.costs?.current?.daily || '$0.00'}
                subtitle={metrics?.costs?.budget?.daily ? `De um orçamento de ${metrics.costs.budget.daily}` : 'Orçamento não definido'}
                variant="cost"
                trend={metrics?.costs?.dailyTrend ? { value: metrics.costs.dailyTrend.value, isPositive: metrics.costs.dailyTrend.isPositive } : undefined}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0 2.08-.402 2.599-1" />
                  </svg>
                }
              />
              
              <MetricCard
                title="Custo Mensal"
                value={metrics?.costs?.current?.monthly || '$0.00'}
                subtitle={metrics?.costs?.budget?.monthly ? `Limite: ${metrics.costs.budget.monthly}` : 'Limite não definido'}
                variant="cost"
                trend={metrics?.costs?.trend ? { value: metrics.costs.trend.value, isPositive: metrics.costs.trend.isPositive } : undefined}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                }
              />

              <MetricCard
                title="Economia Cache"
                value={`$${metrics?.cache?.metrics?.totalSavings?.toFixed(2) || '0.00'}`}
                subtitle="Economizado hoje"
                variant="savings"
                trend={metrics?.cache?.trend ? { value: metrics.cache.trend.value, isPositive: metrics.cache.trend.isPositive } : undefined}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                }
              />
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Seção de Performance */}
        <AccordionItem value="performance">
          <AccordionTrigger className="text-lg font-semibold">
            <div className="flex items-center space-x-2">
              <FaTachometerAlt className="w-5 h-5 text-cyan-600" />
              <span>Performance e Velocidade</span>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-3 gap-2 sm:gap-3 lg:gap-4 xl:gap-2 2xl:gap-3 pt-4">
              <MetricCard
                title="Tempo Resposta"
                value={`${metrics?.cache?.performance?.avgResponseTime || '0'}ms`}
                subtitle="Média das últimas 24h"
                variant="performance"
                trend={metrics?.performance?.responseTimeTrend ? { value: metrics.performance.responseTimeTrend.value, isPositive: metrics.performance.responseTimeTrend.isPositive } : undefined}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                }
              />

              <MetricCard
                title="Mensagens/Hora"
                value={metrics?.summary?.totalMessages || '0'}
                subtitle="Taxa de processamento"
                variant="performance"
                trend={metrics?.usage?.trend ? { value: metrics.usage.trend.value, isPositive: metrics.usage.trend.isPositive } : undefined}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                }
              />

              <MetricCard
                title="Uptime"
                value={`${metrics?.system?.uptime || '0'}%`}
                subtitle="Disponibilidade do serviço"
                variant="performance"
                trend={metrics?.system?.uptimeTrend ? { value: metrics.system.uptimeTrend.value, isPositive: metrics.system.uptimeTrend.isPositive } : undefined}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                }
              />
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Seção de Cache */}
        <AccordionItem value="cache">
          <AccordionTrigger className="text-lg font-semibold">
            <div className="flex items-center space-x-2">
              <FaDatabase className="w-5 h-5 text-blue-600" />
              <span>Cache e Otimização</span>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-3 gap-2 sm:gap-3 lg:gap-4 xl:gap-2 2xl:gap-3 pt-4">
              <MetricCard
                title="Hit Rate"
                value={`${metrics?.cache?.metrics?.hitRate?.toFixed(1) || '0'}%`}
                subtitle="Taxa de acerto do cache"
                variant="cache"
                trend={metrics?.cache?.hitRateTrend ? { value: metrics.cache.hitRateTrend.value, isPositive: metrics.cache.hitRateTrend.isPositive } : undefined}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                  </svg>
                }
              />

              <MetricCard
                title="Entradas Cache"
                value={metrics?.cache?.metrics?.totalRequests?.toLocaleString('pt-BR') || '0'}
                subtitle="Total de entradas"
                variant="cache"
                trend={metrics?.cache?.entriesTrend ? { value: metrics.cache.entriesTrend.value, isPositive: metrics.cache.entriesTrend.isPositive } : undefined}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                }
              />

              <MetricCard
                title="Tamanho Cache"
                value={metrics?.cache?.performance?.memoryUsage || '0MB'}
                subtitle="Uso de memória"
                variant="cache"
                trend={metrics?.cache?.sizeTrend ? { value: metrics.cache.sizeTrend.value, isPositive: metrics.cache.sizeTrend.isPositive } : undefined}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                }
              />
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Resumo Geral */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FaRobot className="w-5 h-5 text-blue-600" />
            <span>Resumo de Utilização</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-3 gap-3 lg:gap-4 xl:gap-2 2xl:gap-3">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {metrics?.usage?.totalMessages || '0'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Mensagens Processadas
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {metrics?.usage?.totalTokens?.toLocaleString('pt-BR') || '0'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Tokens Utilizados
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {metrics?.usage?.activeUsers || '0'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Usuários Ativos
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
