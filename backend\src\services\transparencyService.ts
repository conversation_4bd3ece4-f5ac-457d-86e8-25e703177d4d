import { Client } from 'pg';

interface TransparencyStats {
  totalServidores: number;
  totalDepartamentos: number;
  totalProtocolos: number;
  protocolosAndamento: number;
  protocolosConcluidos: number;
  tempoMedioTramitacao: number;
}

interface DepartmentStats {
  id: number;
  nome: string;
  totalServidores: number;
  totalProtocolos: number;
  protocolosAndamento: number;
  protocolosConcluidos: number;
  eficiencia: number;
}

interface ProtocolStatus {
  situacao: string;
  total: number;
  percentual: number;
}

interface MonthlyVolume {
  mes: string;
  ano: number;
  total: number;
  crescimento: number;
}

interface PopularSubject {
  assunto: string;
  total: number;
  ranking: number;
}

interface PublicProtocolInfo {
  numeroProtocolo: string;
  dataProtocolo: string;
  situacao: string;
  assunto: string;
  ultimaAtualizacao: string;
}

class TransparencyService {
  private config = {
    host: '*************',
    port: 5411,
    user: 'otto',
    password: 'otto',
    database: 'pv_valparaiso'
  };

  /**
   * Criar uma nova conexão para cada operação
   */
  private async createClient(): Promise<Client> {
    const client = new Client(this.config);
    await client.connect();
    return client;
  }

  /**
   * Obter estatísticas gerais do município
   */
  async getGeneralStats(): Promise<TransparencyStats> {
    const client = await this.createClient();
    
    try {
      // Total de servidores ativos
      const servidoresResult = await client.query(`
        SELECT COUNT(*) as total 
        FROM users 
        WHERE ativo = true AND conta_ativa = true
      `);

      // Total de departamentos ativos
      const departamentosResult = await client.query(`
        SELECT COUNT(*) as total 
        FROM departamentos 
        WHERE ativo = true
      `);

      // Estatísticas de protocolos
      const protocolosResult = await client.query(`
        SELECT 
          COUNT(*) as total_protocolos,
          COUNT(CASE WHEN s.descricao ILIKE '%andamento%' OR s.descricao ILIKE '%processo%' THEN 1 END) as em_andamento,
          COUNT(CASE WHEN s.descricao ILIKE '%concluido%' OR s.descricao ILIKE '%finalizado%' THEN 1 END) as concluidos
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        WHERE p.data_protocolo >= CURRENT_DATE - INTERVAL '365 days'
      `);

      // Tempo médio de tramitação (estimado)
      const tempoMedioResult = await client.query(`
        SELECT 
          AVG(EXTRACT(EPOCH FROM (CURRENT_DATE::timestamp - p.data_protocolo::timestamp)) / 86400) as tempo_medio_dias
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        WHERE s.descricao ILIKE '%concluido%' 
          AND p.data_protocolo >= CURRENT_DATE - INTERVAL '90 days'
      `);

      const stats = protocolosResult.rows[0];
      const tempoMedio = tempoMedioResult.rows[0]?.tempo_medio_dias || 15;

      return {
        totalServidores: parseInt(servidoresResult.rows[0].total),
        totalDepartamentos: parseInt(departamentosResult.rows[0].total),
        totalProtocolos: parseInt(stats.total_protocolos || 0),
        protocolosAndamento: parseInt(stats.em_andamento || 0),
        protocolosConcluidos: parseInt(stats.concluidos || 0),
        tempoMedioTramitacao: Math.round(parseFloat(tempoMedio))
      };

    } finally {
      await client.end();
    }
  }

  /**
   * Obter estatísticas por departamento
   */
  async getDepartmentStats(): Promise<DepartmentStats[]> {
    const client = await this.createClient();
    
    try {
      const result = await client.query(`
        SELECT 
          d.id,
          d.descricao as nome,
          COUNT(DISTINCT ud.id_user) as total_servidores,
          COUNT(DISTINCT p.id) as total_protocolos,
          COUNT(DISTINCT CASE WHEN s.descricao ILIKE '%andamento%' THEN p.id END) as protocolos_andamento,
          COUNT(DISTINCT CASE WHEN s.descricao ILIKE '%concluido%' THEN p.id END) as protocolos_concluidos
        FROM departamentos d
        LEFT JOIN user_departamentos ud ON d.id = ud.id_departamento
        LEFT JOIN protocolo_virtual_processos p ON p.data_protocolo >= CURRENT_DATE - INTERVAL '30 days'
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        WHERE d.ativo = true
        GROUP BY d.id, d.descricao
        HAVING COUNT(DISTINCT ud.id_user) > 0
        ORDER BY total_servidores DESC
        LIMIT 20
      `);

      return result.rows.map(row => ({
        id: parseInt(row.id),
        nome: row.nome,
        totalServidores: parseInt(row.total_servidores || 0),
        totalProtocolos: parseInt(row.total_protocolos || 0),
        protocolosAndamento: parseInt(row.protocolos_andamento || 0),
        protocolosConcluidos: parseInt(row.protocolos_concluidos || 0),
        eficiencia: this.calculateEfficiency(
          parseInt(row.protocolos_concluidos || 0),
          parseInt(row.total_protocolos || 0)
        )
      }));

    } finally {
      await client.end();
    }
  }

  /**
   * Obter status dos protocolos (distribuição por situação)
   */
  async getProtocolStatusDistribution(): Promise<ProtocolStatus[]> {
    const client = await this.createClient();
    
    try {
      const result = await client.query(`
        SELECT 
          COALESCE(s.descricao, 'Sem Situação') as situacao,
          COUNT(*) as total
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        WHERE p.data_protocolo >= CURRENT_DATE - INTERVAL '365 days'
        GROUP BY s.descricao
        ORDER BY total DESC
      `);

      const totalProtocolos = result.rows.reduce((sum, row) => sum + parseInt(row.total), 0);

      return result.rows.map((row, index) => ({
        situacao: row.situacao,
        total: parseInt(row.total),
        percentual: Math.round((parseInt(row.total) / totalProtocolos) * 100)
      }));

    } finally {
      await client.end();
    }
  }

  /**
   * Obter volume mensal de protocolos
   */
  async getMonthlyVolume(): Promise<MonthlyVolume[]> {
    const client = await this.createClient();
    
    try {
      const result = await client.query(`
        SELECT 
          TO_CHAR(p.data_protocolo, 'Month') as mes,
          EXTRACT(YEAR FROM p.data_protocolo) as ano,
          EXTRACT(MONTH FROM p.data_protocolo) as mes_num,
          COUNT(*) as total
        FROM protocolo_virtual_processos p
        WHERE p.data_protocolo >= CURRENT_DATE - INTERVAL '12 months'
        GROUP BY 
          TO_CHAR(p.data_protocolo, 'Month'),
          EXTRACT(YEAR FROM p.data_protocolo),
          EXTRACT(MONTH FROM p.data_protocolo)
        ORDER BY ano DESC, mes_num DESC
        LIMIT 12
      `);

      return result.rows.map((row, index) => {
        const current = parseInt(row.total);
        const previous = index < result.rows.length - 1 ? parseInt(result.rows[index + 1].total) : current;
        const crescimento = previous > 0 ? Math.round(((current - previous) / previous) * 100) : 0;

        return {
          mes: row.mes.trim(),
          ano: parseInt(row.ano),
          total: current,
          crescimento
        };
      });

    } finally {
      await client.end();
    }
  }

  /**
   * Obter assuntos mais populares
   */
  async getPopularSubjects(): Promise<PopularSubject[]> {
    const client = await this.createClient();
    
    try {
      const result = await client.query(`
        SELECT 
          COALESCE(a.descricao, 'Assunto Não Informado') as assunto,
          COUNT(*) as total
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        WHERE p.data_protocolo >= CURRENT_DATE - INTERVAL '365 days'
        GROUP BY a.descricao
        ORDER BY total DESC
        LIMIT 10
      `);

      return result.rows.map((row, index) => ({
        assunto: row.assunto,
        total: parseInt(row.total),
        ranking: index + 1
      }));

    } finally {
      await client.end();
    }
  }

  /**
   * Consultar protocolo público (sem dados pessoais)
   */
  async getPublicProtocolInfo(numeroProtocolo: string): Promise<PublicProtocolInfo | null> {
    const client = await this.createClient();
    
    try {
      const result = await client.query(`
        SELECT 
          p.id_protocolo as numero_protocolo,
          p.data_protocolo,
          COALESCE(s.descricao, 'Situação não informada') as situacao,
          COALESCE(a.descricao, 'Assunto não informado') as assunto,
          p.updated_at as ultima_atualizacao
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        WHERE p.id_protocolo = $1
      `, [numeroProtocolo]);

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];
      return {
        numeroProtocolo: row.numero_protocolo,
        dataProtocolo: row.data_protocolo,
        situacao: row.situacao,
        assunto: row.assunto,
        ultimaAtualizacao: row.ultima_atualizacao || row.data_protocolo
      };

    } finally {
      await client.end();
    }
  }

  /**
   * Calcular eficiência de um departamento
   */
  private calculateEfficiency(concluidos: number, total: number): number {
    if (total === 0) return 0;
    return Math.round((concluidos / total) * 100);
  }

  /**
   * Teste de conexão
   */
  async testConnection(): Promise<boolean> {
    try {
      const client = await this.createClient();
      const result = await client.query('SELECT 1 as test');
      await client.end();
      return result.rows.length > 0;
    } catch (error) {
      console.error('Erro ao testar conexão:', error);
      return false;
    }
  }
}

export default new TransparencyService();