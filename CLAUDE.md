# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# PREFEITURA VIRTUAL IA - Municipal AI Chatbot System

## 🎯 Project Overview
AI-powered municipal chatbot for Valparaíso de Goiás Prefecture with multi-tier intelligent caching, strict cost control, and high-performance architecture. Optimized to save up to 65% on AI costs through semantic caching and discount periods.

## 📋 Essential Commands

### Development
```bash
# Install all dependencies (root + workspaces)
npm run install:all

# Full development (backend :3001 + frontend :3000)
npm run dev

# Individual development
cd backend && npm run dev    # Backend with tsx watch
cd frontend && npm run dev   # Frontend Next.js

# Build everything
npm run build

# Quick single test scripts
node test-backend-quick.js       # Quick backend API test
node quick-test.js              # Basic integration test
```

### Testing & Type Checking
```bash
# Run all tests
npm run test

# Individual test suites
cd backend && npm run test          # Jest backend
cd backend && npm run test:watch    # Jest watch mode
cd frontend && npm run test         # Jest frontend
cd frontend && npm run test:watch   # Jest watch mode

# Type checking
cd backend && tsc --noEmit         # Backend TypeScript check
cd frontend && npm run type-check  # Frontend TypeScript check

# Linting (frontend only)
cd frontend && npm run lint
```

### Database Commands (Prisma)
```bash
cd backend
npm run db:generate              # Generate Prisma client
npm run db:push                 # Sync schema with database
npm run db:migrate              # Create and run migrations
npm run db:studio               # Open Prisma Studio GUI
npm run db:create-conversations # Create conversation tables
```

### Access Token Management
```bash
cd backend
npm run create-token   # Create new access token
npm run list-tokens    # List all tokens
npm run revoke-token   # Revoke a token
```

### Test Scripts
```bash
cd backend
# Integration tests
tsx src/scripts/test-database-connection.ts
tsx src/scripts/test-auth-integration.ts
tsx src/scripts/test-chat-complete.ts
tsx src/scripts/test-fluxo-completo.ts

# PostgreSQL analysis
tsx src/scripts/analyze-postgres-structure.ts
tsx src/scripts/verify-real-structure.ts

# RAG system
tsx src/scripts/initialize-rag.ts
tsx src/scripts/test-rag-comparison.ts
```

## 🏗️ High-Level Architecture

### Multi-Tier Cache System
```
┌─────────────────────────────────────────────────┐
│             REQUEST FLOW                         │
├─────────────────────────────────────────────────┤
│ 1. Exact Cache (MD5)     → 100% cost savings    │
│ 2. Semantic Cache (AI)   → 100% cost savings    │
│ 3. Context Cache         → Optimized response   │
│ 4. DeepSeek API          → Variable cost        │
└─────────────────────────────────────────────────┘
```

### Intelligent Queue Prioritization
```
IMMEDIATE → Emergencies (instant processing)
NORMAL    → If discount active: process, else: wait
BATCH     → Always wait for discount (50% savings)
```

### Database Integration
- **PostgreSQL** (*************:5411): Municipal data, users, main structure
- **MongoDB** (*************:2711): Conversations, chat logs, history
- **Redis** (Dual DB): Cache (DB0) + Bull queues (DB1)

## 🔐 Critical Security Patterns

### Inviolable Rules
1. **NEVER** use mock data in production
2. **NEVER** commit credentials (use .env only)
3. **NEVER** insert data directly into DB (always via API)
4. **ALWAYS** validate with Zod schemas
5. **ALWAYS** apply rate limiting
6. **ALWAYS** mask sensitive data in logs
7. **ALWAYS** use SQL parametrized queries ($1, $2, ...) - never concatenate strings
8. **ALWAYS** implement Row Level Security (RLS) for sensitive tables

### Authentication Flow
```typescript
// Hybrid system: mock for dev, PostgreSQL for prod
POST /api/auth/login
{
  "email": "<EMAIL>",  // OR
  "cpf": "12345678900",                   // OR
  "password": "admin123",
  "secretaria": "administracao"
}
```

### Rate Limits
- Login: 5 attempts/15min
- Chat: 30 msgs/min
- Dashboard: 50 req/5min
- Admin: 10 actions/30min

## 💰 Cost Optimization System

### Discount Hours (50% savings)
- **Local Time**: 13:30 - 21:30 (Brasília)
- **UTC Time**: 16:30 - 00:30
- **Savings**: $0.00219/msg → $0.001095/msg

### DeepSeek V3 Pricing
```
Normal:
- Input: $0.07/1M tokens
- Output: $1.10/1M tokens

Discount (50%):
- Input: $0.035/1M tokens  
- Output: $0.55/1M tokens
```

## 🛠️ Development Patterns

### Backend Route Pattern
```typescript
router.post("/endpoint",
  authenticate,           // JWT validation
  authorize("admin"),     // Role check
  auditAccess("action"),  // Log access
  validateRequest(schema), // Zod validation
  controller             // Business logic
);
```

### Frontend Component Pattern
```typescript
// Container (logic) - Always use functional components with hooks
export function FeatureContainer() {
  const logic = useFeatureLogic();
  return <Feature {...presentationalProps} />;
}

// Presentational (UI only) - Max 300 lines per component
export function Feature({ data, onAction, isLoading }: FeatureProps) {
  return <div>{/* Pure UI */}</div>;
}
```

### TypeScript Path Configuration
```typescript
// Backend (@/* maps to src/*)
import { Controller } from '@/controllers/Controller';
import { Service } from '@/services/Service';

// Frontend (@/* maps to src/*)
import { Component } from '@/components/Component';
import { useHook } from '@/hooks/useHook';
```

### SQL Pattern (PostgreSQL)
```typescript
// ALWAYS use parametrized queries - NEVER concatenate strings
const result = await pool.query(
  'SELECT * FROM users WHERE id = $1 AND secretaria = $2',
  [userId, secretaria]
);

// Use snake_case for tables and columns
// Tables: singular (user, conversation, access_token)
// PKs: always 'id', FKs: '{table}_id'
```

### Zod Validation Pattern
```typescript
const messageSchema = z.object({
  message: z.string().min(1).max(1000),
  userId: z.string(),
  secretaria: z.enum(['administracao', 'saude', 'educacao']),
  forceImmediate: z.boolean().optional()
});
```

## 📂 Directory Structure

### Backend Architecture
```
backend/src/
├── config/         # Redis, database configurations
├── controllers/    # Route handlers (chatController, dashboardController, etc.)
├── services/       # Business logic (deepSeekService, cacheService, ragService)
├── repositories/   # Data access layer (PostgreSQLRepository)
├── middleware/     # Auth, cache, rate limit, error handling
├── routes/         # Express route definitions
├── scripts/        # Utility scripts (testing, DB setup, token management)
└── types/          # TypeScript interfaces and types
```

### Frontend Architecture
```
frontend/src/
├── app/           # Next.js 14 app router pages
├── components/    # Reusable UI components
├── hooks/         # Custom React hooks (useChat, useAuth, useMetrics)
├── services/      # API client services
└── types/         # TypeScript interfaces
```

### Key Service Files
- `deepSeekService.ts` - AI integration with cost monitoring
- `cacheService.ts` - Multi-tier cache (exact, semantic, context)
- `ragService.ts` - RAG system for municipal knowledge
- `urgencyClassifier.ts` - Message priority classification
- `costMonitoringService.ts` - Cost tracking and alerts

## 🔍 Debugging & Monitoring

### Real-time Logs
```bash
# Application logs
tail -f backend/logs/app-$(date +%Y-%m-%d).log

# Security logs
tail -f backend/logs/security-$(date +%Y-%m-%d).log

# Redis cache monitoring
redis-cli monitor | grep -E "(GET|SET|DEL)"

# API metrics
curl -s http://localhost:3001/api/chat/metrics | jq .
```

### Redis Commands
```bash
# Check cache keys
redis-cli -n 0 KEYS "*cache*"     # Cache DB
redis-cli -n 1 KEYS "*queue*"     # Queue DB

# Clear specific cache
redis-cli -n 0 DEL "exact_cache:*"
redis-cli -n 0 DEL "semantic_cache:*"
```

## 🔧 Environment Variables

Critical variables that must be configured:

```bash
# AI Services
DEEPSEEK_API_KEY=
OPENAI_API_BASE_URL=https://api.deepseek.com
OPENAI_API_KEY=              # For embeddings

# Databases
DATABASE_URL=postgresql://...
MONGODB_URI=mongodb://...
REDIS_URL=redis://...
REDIS_HOST=localhost
REDIS_PORT=6379

# Authentication
JWT_SECRET=
JWT_EXPIRES_IN=24h

# Cache Configuration
CACHE_TTL_EXACT=86400       # 24 hours
CACHE_TTL_SEMANTIC=43200    # 12 hours
DISCOUNT_START_HOUR=16      # UTC
DISCOUNT_END_HOUR=0         # UTC

# Cost Control
MAX_DAILY_BUDGET=50.00
EMERGENCY_STOP_BUDGET=100.00
USER_MESSAGES_PER_DAY=200
```

## 🤖 Claude-Flow Integration

The project includes Claude-Flow support with Node.js 22:

### Claude-Flow Commands
```bash
# System status
./claude-flow status

# SPARC modes
./claude-flow sparc run architect "design system"
./claude-flow sparc run code "implement feature"
./claude-flow sparc run tdd "create tests"

# Memory system
./claude-flow memory store key "value"
./claude-flow memory query term
```

### Available SPARC Modes
- architect, code, tdd, security-review, integration, devops
- ask, research, debug, review, optimize, document
- migrate, refactor, test, analyze, design

## ⚡ Performance Guidelines

1. **Cache First**: Always check cache before API calls
2. **Batch Operations**: Group operations when possible
3. **Async Patterns**: Use async/await, avoid callbacks
4. **Error Handling**: Implement proper error boundaries
5. **Logging Levels**: Use appropriate levels (info/warn/error)

## 📚 Key Documentation

- `/docs/README.md` - Complete documentation index
- `/docs/redis-architecture.md` - Redis architecture details
- `/docs/database-integration-plan.md` - DB integration plan
- `/docs/postgres-integration-report.md` - PostgreSQL report
- `/README.md` - Project overview and setup

## 🎯 Development Language Requirement

**CRITICAL**: All communication, comments, documentation, and user-facing text must be in **Portuguese (PT-BR)**. This is a Brazilian municipal system and Portuguese is mandatory for all development work.

## 🔧 Code Quality Standards

### Pre-commit Checklist
- [ ] TypeScript compilation without errors (`tsc --noEmit`)
- [ ] All components properly typed with interfaces
- [ ] SQL queries use parametrized form ($1, $2, ...) 
- [ ] RLS implemented for new sensitive tables
- [ ] Token authentication validated on protected routes
- [ ] No hardcoded credentials or sensitive data
- [ ] Zod validation applied to all inputs

### Architecture Principles
1. **Separation of Concerns**: Controllers handle routes, services contain business logic
2. **Type Safety**: Everything must be properly typed, avoid `any`
3. **Database Security**: Always use RLS and parametrized queries
4. **Cost Awareness**: Consider cache implications and AI costs in all changes
5. **Error Handling**: Implement proper error boundaries and logging

---

**IMPORTANT**: This system prioritizes cost savings, security, and performance. Always consider cache impact and AI costs when making modifications.