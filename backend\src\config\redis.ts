import Redis from 'ioredis';
import { config } from 'dotenv';

config();

// Configuração do Redis para cache e filas
const redisConfig = {
  host: process.env['REDIS_HOST'] || 'localhost',
  port: parseInt(process.env['REDIS_PORT'] || '6379'),
  password: process.env['REDIS_PASSWORD'] || undefined,
  db: 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  connectTimeout: 5000, // 5 seconds
  commandTimeout: 3000, // 3 seconds
  retryDelayOnClusterDown: 300,
  maxRetriesPerRequest: 3,
};

// Instância principal do Redis para cache
export const redisCache = new Redis({
  ...redisConfig,
  ...(redisConfig.password && { password: redisConfig.password })
});

// Instância separada para filas (Bull)
export const redisQueue = new Redis({
  ...redisConfig,
  db: 1, // Usar database diferente para filas
  ...(redisConfig.password && { password: redisConfig.password })
});

// Configuração de TTL por tipo de cache
export const cacheTTL = {
  EXACT_MATCH: 24 * 60 * 60, // 24 horas
  SEMANTIC_MATCH: 12 * 60 * 60, // 12 horas
  CONTEXT_CACHE: 60 * 60, // 1 hora
  USER_SESSION: 30 * 60, // 30 minutos
  METRICS: 5 * 60, // 5 minutos
};

// Prefixos para organizar chaves no Redis
export const cacheKeys = {
  EXACT: (hash: string) => `exact:${hash}`,
  SEMANTIC: (secretaria: string, topic: string) => `semantic:${secretaria}:${topic}`,
  CONTEXT: (secretaria: string) => `context:${secretaria}`,
  METRICS: (type: string, date: string) => `metrics:${type}:${date}`,
  QUEUE: (type: string) => `queue:${type}`,
  USER_LIMIT: (userId: string, type: string) => `limit:${userId}:${type}`,
};

// Configurações de economia por horário
export const discountConfig = {
  // Horários UTC para desconto (16:30-00:30 UTC)
  DISCOUNT_START_HOUR: 16.5,
  DISCOUNT_END_HOUR: 0.5,
  DISCOUNT_RATE: 0.5, // 50% desconto
  
  // Custos DeepSeek V3 (em USD)
  COSTS: {
    INPUT_CACHE_HIT: 0.07 / 1000000, // $0.07 per 1M tokens
    INPUT_CACHE_MISS: 0.27 / 1000000, // $0.27 per 1M tokens  
    OUTPUT: 1.10 / 1000000, // $1.10 per 1M tokens
    
    // Com desconto (50% off)
    INPUT_CACHE_HIT_DISCOUNT: 0.035 / 1000000,
    INPUT_CACHE_MISS_DISCOUNT: 0.135 / 1000000,
    OUTPUT_DISCOUNT: 0.550 / 1000000,
  }
};

// Função para verificar se está em horário de desconto
export const isDiscountTime = (): boolean => {
  const now = new Date();
  const utcHour = now.getUTCHours() + now.getUTCMinutes() / 60;
  
  // 16:30 UTC até 00:30 UTC (próximo dia)
  return utcHour >= discountConfig.DISCOUNT_START_HOUR || 
         utcHour < discountConfig.DISCOUNT_END_HOUR;
};

// Função para calcular próximo horário de desconto
export const getNextDiscountTime = (): Date => {
  const now = new Date();
  const utcHour = now.getUTCHours() + now.getUTCMinutes() / 60;
  
  let nextDiscount = new Date();
  
  if (utcHour < discountConfig.DISCOUNT_END_HOUR) {
    // Já estamos no período de desconto, termina às 00:30
    nextDiscount.setUTCHours(0, 30, 0, 0);
  } else if (utcHour >= discountConfig.DISCOUNT_START_HOUR) {
    // Estamos no período de desconto, termina às 00:30 do próximo dia
    nextDiscount.setUTCDate(nextDiscount.getUTCDate() + 1);
    nextDiscount.setUTCHours(0, 30, 0, 0);
  } else {
    // Próximo desconto começa às 16:30 hoje
    nextDiscount.setUTCHours(16, 30, 0, 0);
  }
  
  return nextDiscount;
};

// Event handlers para Redis
redisCache.on('connect', () => {
  console.log('✅ Redis Cache conectado');
});

redisCache.on('error', (err) => {
  console.error('❌ Redis Cache erro:', err);
});

redisQueue.on('connect', () => {
  console.log('✅ Redis Queue conectado');
});

redisQueue.on('error', (err) => {
  console.error('❌ Redis Queue erro:', err);
});

export default { redisCache, redisQueue };