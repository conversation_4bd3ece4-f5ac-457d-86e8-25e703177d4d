import { processMessage } from '../services/deepSeekService';
import { config } from 'dotenv';

config();

console.log('🔍 TESTANDO CORREÇÃO DO CONTEXTO DE CONVERSA');
console.log('==========================================\n');

async function testarContextoConversa() {
  const context = {
    userId: '1',
    secretaria: 'administracao'
  };
  
  try {
    console.log('📌 SIMULANDO O FLUXO DE CONVERSA PROBLEMÁTICO');
    console.log('=============================================\n');
    
    // 1. Primeira pergunta - sobre protocolo específico
    console.log('1️⃣ PERGUNTA: "detalhes do PROTOCOLO 20250060168"');
    console.log('---------------------------------------------------');
    const resposta1 = await processMessage('me de detalhes do PROTOCOLO 20250060168', context);
    
    // Extrair dados da primeira resposta
    const cnpjMatch1 = resposta1.content.match(/CNPJ[:\s]*(\d{2}\.?\d{3}\.?\d{3}\/?\d{4}-?\d{2})/i);
    const empresaMatch1 = resposta1.content.match(/(?:Requerente|Empresa)[:\s]*([A-Z\s]+)/i);
    
    console.log('✅ Primeira resposta (resumo):');
    if (empresaMatch1) console.log(`   Empresa: ${empresaMatch1[1].trim()}`);
    if (cnpjMatch1) console.log(`   CNPJ: ${cnpjMatch1[1]}`);
    
    console.log('\n2️⃣ PERGUNTA: "nome dos sócios do CNPJ 44.067.277/0001-07"');
    console.log('-----------------------------------------------------------');
    const resposta2 = await processMessage('Qual o nome dos socios do cnpj: 44.067.277/0001-07?', context);
    
    // Verificar se manteve o mesmo CNPJ
    const cnpjMatch2 = resposta2.content.match(/44\.067\.277\/0001-07/);
    console.log(`✅ Segunda resposta mantém CNPJ correto? ${cnpjMatch2 ? 'SIM' : 'NÃO'}`);
    
    console.log('\n3️⃣ PERGUNTA CRÍTICA: "Essa empresa é o que? qual tipo de serviço ela faz?"');
    console.log('------------------------------------------------------------------------');
    const resposta3 = await processMessage('Essa empresa é o que? qual tipo de serviço ela faz?', context);
    
    // Verificar se manteve a mesma empresa
    const mantemCNPJCorreto = resposta3.content.includes('44.067.277/0001-07');
    const mudouEmpresa = resposta3.content.includes('EQUILIBRIO COMERCIO E SERVICO') || 
                        resposta3.content.includes('53.636.187/0001-80');
    
    console.log('📊 ANÁLISE DA TERCEIRA RESPOSTA:');
    console.log(`   ✅ Mantém CNPJ correto (44.067.277/0001-07)? ${mantemCNPJCorreto ? 'SIM' : 'NÃO'}`);
    console.log(`   ❌ Mudou para empresa errada? ${mudouEmpresa ? 'SIM (ERRO!)' : 'NÃO (correto)'}`);
    
    // Mostrar parte da resposta para análise
    console.log('\n📄 TRECHO DA TERCEIRA RESPOSTA:');
    console.log('--------------------------------');
    console.log(resposta3.content.substring(0, 300) + '...');
    
    // Resultado final
    console.log('\n🎯 RESULTADO DO TESTE:');
    console.log('======================');
    if (mantemCNPJCorreto && !mudouEmpresa) {
      console.log('✅ SUCESSO: Contexto de conversa mantido corretamente!');
      console.log('✅ A correção funcionou - não houve mudança de empresa.');
    } else if (mudouEmpresa) {
      console.log('❌ ERRO PERSISTENTE: IA ainda está mudando de empresa!');
      console.log('❌ Necessário ajustar mais o sistema de contexto.');
    } else {
      console.log('⚠️ RESULTADO PARCIAL: Melhorou mas ainda precisa ajustes.');
    }
    
  } catch (error: any) {
    console.error('\n❌ Erro durante o teste:', error.message);
  }
}

// Executar teste
testarContextoConversa().catch(console.error);