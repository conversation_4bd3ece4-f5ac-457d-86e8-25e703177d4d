#!/usr/bin/env tsx

/**
 * Script para revogar token de acesso
 * Uso: npm run revoke-token <token>
 */

import { accessTokenService } from '../services/accessTokenService';

async function revokeAccessToken() {
  const token = process.argv[2];

  if (!token) {
    console.error('❌ Token não fornecido');
    console.log('💡 Uso: npm run revoke-token <token>');
    console.log('💡 Para listar tokens: npm run list-tokens');
    process.exit(1);
  }

  console.log(`🔐 Revogando token de acesso: ${token.substring(0, 8)}...${token.substring(token.length - 4)}\n`);

  try {
    // Verificar integridade do token antes de revogar
    const verification = await accessTokenService.verifyTokenIntegrity(token);
    
    if (!verification.valid) {
      console.log('⚠️  Token já está inválido ou não existe');
      console.log(`   Erro: ${verification.error}`);
      process.exit(0);
    }

    console.log('ℹ️  Informações do token:');
    console.log(`   👤 Usuário: ${verification.user.name}`);
    console.log(`   📝 Descrição: ${verification.user.description || 'N/A'}`);
    console.log(`   🆔 User ID: ${verification.user.id}\n`);

    // Confirmar revogação
    console.log('⚠️  ATENÇÃO: Esta ação não pode ser desfeita!');
    console.log('   • O usuário perderá acesso permanentemente');
    console.log('   • As conversas serão mantidas no banco de dados');
    console.log('   • Um novo token precisará ser criado para restaurar o acesso\n');

    // Em produção, adicionar confirmação interativa aqui
    // Para o script, vamos revogar diretamente
    
    const revoked = await accessTokenService.revokeAccessToken(token);

    if (revoked) {
      console.log('✅ Token revogado com sucesso!');
      console.log('   • O usuário não conseguirá mais acessar o chatbot');
      console.log('   • As conversas existentes foram preservadas');
      console.log('   • Para restaurar o acesso, crie um novo token\n');
      
      console.log('📊 PRÓXIMOS PASSOS:');
      console.log('• Verificar tokens ativos: npm run list-tokens');
      console.log('• Criar novo token: npm run create-token "Nova descrição"');
    } else {
      console.log('❌ Falha ao revogar token');
      console.log('   • Token pode não existir no banco de dados');
      console.log('   • Verifique os logs do servidor para mais detalhes');
    }

  } catch (error) {
    console.error('❌ Erro ao revogar token:', error);
    process.exit(1);
  }
}

// Executar apenas se chamado diretamente
if (require.main === module) {
  revokeAccessToken().then(() => {
    console.log('\n🎉 Processo de revogação concluído!');
    process.exit(0);
  }).catch((error) => {
    console.error('\n💥 Erro fatal:', error);
    process.exit(1);
  });
}