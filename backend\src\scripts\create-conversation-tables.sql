-- <PERSON><PERSON><PERSON> para criar tabelas de conversas no PostgreSQL
-- Banco: pv_valparaiso
-- Data: 2025-01-28

-- Drop tables if exist (cuidado em produção!)
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS conversations CASCADE;

-- <PERSON>riar tabela de conversas
CREATE TABLE conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER REFERENCES usuarios(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_message TEXT,
  message_count INTEGER DEFAULT 0,
  status VARCHAR(50) DEFAULT 'active' -- active, archived, deleted
);

-- <PERSON>riar tabel<PERSON> de mensagens
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  role VARCHAR(50) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  tokens_used INTEGER,
  cost DECIMAL(10, 6),
  cache_hit BOOLEAN DEFAULT FALSE,
  cache_type VARCHAR(50), -- exact, semantic, null
  processing_time INTEGER, -- milliseconds
  metadata JSONB DEFAULT '{}'::jsonb
);

-- Índices para melhor performance
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_created_at ON conversations(created_at DESC);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX idx_messages_role ON messages(role);

-- Trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE
  ON conversations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Função para atualizar last_message e message_count
CREATE OR REPLACE FUNCTION update_conversation_stats()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE conversations 
    SET 
      last_message = NEW.content,
      message_count = message_count + 1,
      updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.conversation_id;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE conversations 
    SET 
      message_count = message_count - 1,
      updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.conversation_id;
  END IF;
  RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_conversation_stats_trigger
AFTER INSERT OR DELETE ON messages
FOR EACH ROW EXECUTE FUNCTION update_conversation_stats();

-- Comentários nas tabelas
COMMENT ON TABLE conversations IS 'Tabela para armazenar conversas do chatbot';
COMMENT ON TABLE messages IS 'Tabela para armazenar mensagens individuais das conversas';
COMMENT ON COLUMN conversations.status IS 'Status da conversa: active, archived, deleted';
COMMENT ON COLUMN messages.role IS 'Papel do remetente: user, assistant, system';
COMMENT ON COLUMN messages.cache_type IS 'Tipo de cache usado: exact, semantic, ou null se não foi cache';
COMMENT ON COLUMN messages.metadata IS 'Dados adicionais em formato JSON: secretaria, urgency, etc';

-- Dados de exemplo (opcional - remover em produção)
/*
INSERT INTO conversations (user_id, title) 
VALUES 
  (1, 'Consulta sobre departamentos'),
  (1, 'Informações sobre funcionários');

-- Verificar criação
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name IN ('conversations', 'messages')
ORDER BY table_name, ordinal_position;
*/