import { Router } from 'express';
import { authenticate } from '../middleware/authenticateMiddleware';
import { flexibleAuth } from '../middleware/tokenAuthMiddleware';
import { postgresRepository } from '../repositories/PostgreSQLRepository';
import { z } from 'zod';

const router = Router();

// Schemas de validação
const createConversationSchema = z.object({
  title: z.string().min(1).max(255).optional()
});

const updateTitleSchema = z.object({
  title: z.string().min(1).max(255)
});

/**
 * @route GET /api/conversations
 * @desc Listar conversas do usuário autenticado
 * @access Private
 */
router.get('/', flexibleAuth, async (req, res) => {
  try {
    const userId = (req as any).user.id;
    const limit = parseInt(req.query.limit as string) || 50;
    
    const conversations = await postgresRepository.getUserConversations(userId, limit);
    
    res.json({
      success: true,
      data: conversations
    });
  } catch (error: any) {
    console.error('Erro ao buscar conversas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar conversas'
    });
  }
});

/**
 * @route POST /api/conversations
 * @desc Criar nova conversa
 * @access Private
 */
router.post('/', flexibleAuth, async (req, res) => {
  try {
    const userId = (req as any).user.id;
    const validatedData = createConversationSchema.parse(req.body);
    const title = validatedData.title || 'Nova Conversa';
    
    const conversation = await postgresRepository.createConversation(userId, title);
    
    res.status(201).json({
      success: true,
      data: conversation
    });
  } catch (error: any) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Dados inválidos',
        details: error.errors
      });
    }
    
    console.error('Erro ao criar conversa:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao criar conversa'
    });
  }
});

/**
 * @route GET /api/conversations/active
 * @desc Buscar ou criar conversa ativa
 * @access Private
 */
router.get('/active', flexibleAuth, async (req, res) => {
  try {
    const userId = (req as any).user.id;
    
    const conversation = await postgresRepository.getOrCreateActiveConversation(userId);
    
    res.json({
      success: true,
      data: conversation
    });
  } catch (error: any) {
    console.error('Erro ao buscar conversa ativa:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar conversa ativa'
    });
  }
});

/**
 * @route GET /api/conversations/:id
 * @desc Buscar conversa específica
 * @access Private
 */
router.get('/:id', flexibleAuth, async (req, res) => {
  try {
    const userId = (req as any).user.id;
    const conversationId = req.params.id;
    
    const conversation = await postgresRepository.getConversationById(conversationId, userId);
    
    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: 'Conversa não encontrada'
      });
    }
    
    res.json({
      success: true,
      data: conversation
    });
  } catch (error: any) {
    console.error('Erro ao buscar conversa:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar conversa'
    });
  }
});

/**
 * @route GET /api/conversations/:id/messages
 * @desc Buscar mensagens de uma conversa
 * @access Private
 */
router.get('/:id/messages', flexibleAuth, async (req, res) => {
  try {
    const userId = (req as any).user.id;
    const conversationId = req.params.id;
    const limit = parseInt(req.query.limit as string) || 100;
    
    // Verificar se o usuário tem acesso à conversa
    const conversation = await postgresRepository.getConversationById(conversationId, userId);
    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: 'Conversa não encontrada'
      });
    }
    
    const messages = await postgresRepository.getConversationMessages(conversationId, limit);
    
    res.json({
      success: true,
      data: messages
    });
  } catch (error: any) {
    console.error('Erro ao buscar mensagens:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar mensagens'
    });
  }
});

/**
 * @route PUT /api/conversations/:id/title
 * @desc Atualizar título da conversa
 * @access Private
 */
router.put('/:id/title', flexibleAuth, async (req, res) => {
  try {
    const userId = (req as any).user.id;
    const conversationId = req.params.id;
    const validatedData = updateTitleSchema.parse(req.body);
    
    const updated = await postgresRepository.updateConversationTitle(
      conversationId, 
      validatedData.title, 
      userId
    );
    
    if (!updated) {
      return res.status(404).json({
        success: false,
        error: 'Conversa não encontrada'
      });
    }
    
    res.json({
      success: true,
      message: 'Título atualizado com sucesso'
    });
  } catch (error: any) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Dados inválidos',
        details: error.errors
      });
    }
    
    console.error('Erro ao atualizar título:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao atualizar título'
    });
  }
});

/**
 * @route DELETE /api/conversations/:id
 * @desc Deletar conversa (soft delete)
 * @access Private
 */
router.delete('/:id', flexibleAuth, async (req, res) => {
  try {
    const userId = (req as any).user.id;
    const conversationId = req.params.id;
    
    const deleted = await postgresRepository.deleteConversation(conversationId, userId);
    
    if (!deleted) {
      return res.status(404).json({
        success: false,
        error: 'Conversa não encontrada'
      });
    }
    
    res.json({
      success: true,
      message: 'Conversa deletada com sucesso'
    });
  } catch (error: any) {
    console.error('Erro ao deletar conversa:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao deletar conversa'
    });
  }
});

/**
 * @route GET /api/conversations/:id/export
 * @desc Exportar conversa em formato texto
 * @access Private
 */
router.get('/:id/export', flexibleAuth, async (req, res) => {
  try {
    const userId = (req as any).user.id;
    const conversationId = req.params.id;
    
    // Verificar acesso
    const conversation = await postgresRepository.getConversationById(conversationId, userId);
    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: 'Conversa não encontrada'
      });
    }
    
    // Buscar todas as mensagens
    const messages = await postgresRepository.getConversationMessages(conversationId, 1000);
    
    // Formatar como texto
    let exportText = `Conversa: ${conversation.title}\n`;
    exportText += `Data: ${new Date(conversation.created_at).toLocaleString('pt-BR')}\n`;
    exportText += `Total de mensagens: ${messages.length}\n`;
    exportText += '='.repeat(50) + '\n\n';
    
    messages.forEach(msg => {
      const date = new Date(msg.created_at).toLocaleString('pt-BR');
      const role = msg.role === 'user' ? 'Você' : 'Assistente';
      exportText += `[${date}] ${role}:\n${msg.content}\n\n`;
    });
    
    // Enviar como download
    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="conversa-${conversationId}.txt"`);
    res.send(exportText);
    
  } catch (error: any) {
    console.error('Erro ao exportar conversa:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao exportar conversa'
    });
  }
});

export default router;