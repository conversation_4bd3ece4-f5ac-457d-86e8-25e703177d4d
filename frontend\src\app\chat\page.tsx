'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import ChatHeader from '@/components/chat/ChatHeader';
import MessagesArea from '@/components/chat/MessagesArea';
import InputBar from '@/components/chat/InputBar';
import { Sidebar } from '@/components/Sidebar';
import { useChat } from '@/hooks/useChat';
import { useTokenAuth } from '@/hooks/useTokenAuth';
import { FaSpinner } from 'react-icons/fa';

// Loading Screen Component
function LoadingScreen() {
  return (
    <div className="h-screen flex items-center justify-center bg-gray-50 dark:bg-brand-dark">
      <div className="text-center">
        <FaSpinner className="text-4xl text-brand animate-spin mx-auto mb-4" />
        <p className="text-gray-600 dark:text-gray-300">Carregando...</p>
      </div>
    </div>
  );
}

// Error Screen Component
function ErrorScreen({ message }: { message: string }) {
  const router = useRouter();
  
  return (
    <div className="h-screen flex items-center justify-center bg-gray-50 dark:bg-brand-dark">
      <div className="text-center max-w-md">
        <h2 className="text-2xl font-bold text-red-400 mb-4">Erro de Autenticação</h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">{message}</p>
        <button
          onClick={() => router.push('/')}
          className="px-6 py-2 bg-brand text-white rounded-lg hover:bg-brand-600 transition-colors"
        >
          Voltar ao Início
        </button>
      </div>
    </div>
  );
}

export default function ChatPage() {
  const { token, user, isLoading: authLoading, error: authError } = useTokenAuth();
  const { 
    messages, 
    isLoading: chatLoading, 
    sendMessage, 
    clearMessages,
    copiedMessageId,
    handleCopyMessage 
  } = useChat(token || '', user);
  
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);

  // Handle authentication errors
  useEffect(() => {
    if (!authLoading && (!token || authError)) {
      // Token not found or invalid
      console.error('Authentication error:', authError);
    }
  }, [authLoading, token, authError]);

  if (authLoading) return <LoadingScreen />;
  if (authError || !token) {
    return <ErrorScreen message={authError || 'Token de acesso não encontrado'} />;
  }

  const handleQuickAction = (question: string) => {
    sendMessage(question);
  };

  return (
    <div className="h-screen flex bg-gray-50 dark:bg-brand-dark">
      {/* Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        onConversationSelect={(id: string) => {
          setConversationId(id);
          // Só fecha o sidebar no mobile
          if (window.innerWidth < 1024) {
            setSidebarOpen(false);
          }
        }}
        onNewConversation={() => {
          setConversationId(null);
          clearMessages();
          // Só fecha o sidebar no mobile
          if (window.innerWidth < 1024) {
            setSidebarOpen(false);
          }
        }}
        activeConversationId={conversationId || undefined}
      />
      
      {/* Main Content */}
      <motion.div 
        className="flex-1 flex flex-col min-w-0"
        animate={{ 
          x: sidebarOpen && window.innerWidth >= 1024 ? 0 : 0,
        }}
        transition={{ 
          duration: 0.4, 
          ease: [0.25, 0.46, 0.45, 0.94] 
        }}
      >
        {/* Header */}
        <ChatHeader 
          user={user} 
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} 
        />
        
        {/* Messages Area */}
        <MessagesArea 
          messages={messages}
          isLoading={chatLoading}
          onQuickAction={handleQuickAction}
          copiedMessageId={copiedMessageId}
          onCopyMessage={handleCopyMessage}
        />
        
        {/* Input Bar */}
        <InputBar 
          onSend={sendMessage}
          isLoading={chatLoading}
          messageCount={messages.length}
          onClear={clearMessages}
        />
      </motion.div>
    </div>
  );
}