import { Client } from 'pg';
import { config } from 'dotenv';

config();

const postgresConfig = {
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso'
};

async function checkConversationsTable() {
  console.log('🔍 VERIFICANDO ESTRUTURA DE CONVERSAS');
  console.log('=====================================\n');
  
  const client = new Client(postgresConfig);
  
  try {
    await client.connect();
    
    // 1. Verificar se a tabela conversations existe
    console.log('1️⃣ Verificando se tabela conversations existe...');
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'conversations'
      );
    `);
    
    const conversationsExists = tableCheck.rows[0].exists;
    console.log(`Tabela conversations existe? ${conversationsExists ? '✅ SIM' : '❌ NÃO'}`);
    
    if (!conversationsExists) {
      console.log('\n⚠️ Tabela conversations não existe! Criando estrutura...');
      
      // Criar tabela conversations
      await client.query(`
        CREATE TABLE IF NOT EXISTS conversations (
          id SERIAL PRIMARY KEY,
          user_id INTEGER NOT NULL,
          title VARCHAR(255) DEFAULT 'Nova Conversa',
          status VARCHAR(50) DEFAULT 'active',
          last_message TEXT,
          message_count INTEGER DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
      
      console.log('✅ Tabela conversations criada!');
      
      // Criar índice
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
      `);
      
      console.log('✅ Índice criado!');
    }
    
    // 2. Verificar se a tabela messages existe
    console.log('\n2️⃣ Verificando se tabela messages existe...');
    const messagesCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'messages'
      );
    `);
    
    const messagesExists = messagesCheck.rows[0].exists;
    console.log(`Tabela messages existe? ${messagesExists ? '✅ SIM' : '❌ NÃO'}`);
    
    if (!messagesExists) {
      console.log('\n⚠️ Tabela messages não existe! Criando estrutura...');
      
      // Criar tabela messages
      await client.query(`
        CREATE TABLE IF NOT EXISTS messages (
          id SERIAL PRIMARY KEY,
          conversation_id INTEGER NOT NULL,
          role VARCHAR(20) NOT NULL,
          content TEXT NOT NULL,
          tokens_used INTEGER,
          cost DECIMAL(10,6),
          cache_hit BOOLEAN DEFAULT FALSE,
          processing_time INTEGER,
          metadata JSONB,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
      
      console.log('✅ Tabela messages criada!');
      
      // Criar índice
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
      `);
      
      console.log('✅ Índice criado!');
    }
    
    // 3. Verificar estrutura das tabelas
    if (conversationsExists) {
      console.log('\n3️⃣ Estrutura da tabela conversations:');
      const columnsResult = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = 'conversations'
        ORDER BY ordinal_position;
      `);
      
      columnsResult.rows.forEach(col => {
        console.log(`   - ${col.column_name} (${col.data_type})`);
      });
    }
    
    // 4. Verificar dados de exemplo
    console.log('\n4️⃣ Verificando dados existentes...');
    const countResult = await client.query('SELECT COUNT(*) as total FROM conversations');
    console.log(`Total de conversas: ${countResult.rows[0].total}`);
    
    if (countResult.rows[0].total > 0) {
      const sampleResult = await client.query(`
        SELECT id, user_id, title, status, message_count, created_at
        FROM conversations
        ORDER BY created_at DESC
        LIMIT 5;
      `);
      
      console.log('\nÚltimas 5 conversas:');
      sampleResult.rows.forEach(conv => {
        console.log(`   - ID: ${conv.id}, User: ${conv.user_id}, Título: ${conv.title}, Mensagens: ${conv.message_count}`);
      });
    }
    
  } catch (error: any) {
    console.error('❌ Erro:', error.message);
  } finally {
    await client.end();
  }
  
  console.log('\n✅ Verificação concluída!');
}

// Executar verificação
checkConversationsTable().catch(console.error);