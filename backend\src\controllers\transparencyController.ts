import { Request, Response } from 'express';
import transparencyService from '../services/transparencyService';

class TransparencyController {
  /**
   * Obter estatísticas gerais do município
   * GET /api/public/stats/general
   */
  async getGeneralStats(req: Request, res: Response): Promise<void> {
    try {
      console.log('📊 Buscando estatísticas gerais do município...');
      
      const stats = await transparencyService.getGeneralStats();
      
      res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
        message: 'Estatísticas gerais obtidas com sucesso'
      });

    } catch (error: any) {
      console.error('❌ Erro ao obter estatísticas gerais:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: 'Não foi possível obter as estatísticas gerais'
      });
    }
  }

  /**
   * Obter estatísticas por departamento
   * GET /api/public/stats/departments
   */
  async getDepartmentStats(req: Request, res: Response): Promise<void> {
    try {
      console.log('🏢 Buscando estatísticas por departamento...');
      
      const stats = await transparencyService.getDepartmentStats();
      
      res.json({
        success: true,
        data: stats,
        count: stats.length,
        timestamp: new Date().toISOString(),
        message: 'Estatísticas por departamento obtidas com sucesso'
      });

    } catch (error: any) {
      console.error('❌ Erro ao obter estatísticas por departamento:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: 'Não foi possível obter as estatísticas por departamento'
      });
    }
  }

  /**
   * Obter distribuição de status dos protocolos
   * GET /api/public/protocols/status
   */
  async getProtocolStatusDistribution(req: Request, res: Response): Promise<void> {
    try {
      console.log('📋 Buscando distribuição de status dos protocolos...');
      
      const distribution = await transparencyService.getProtocolStatusDistribution();
      
      res.json({
        success: true,
        data: distribution,
        count: distribution.length,
        timestamp: new Date().toISOString(),
        message: 'Distribuição de status obtida com sucesso'
      });

    } catch (error: any) {
      console.error('❌ Erro ao obter distribuição de status:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: 'Não foi possível obter a distribuição de status'
      });
    }
  }

  /**
   * Obter volume mensal de protocolos
   * GET /api/public/protocols/volume
   */
  async getMonthlyVolume(req: Request, res: Response): Promise<void> {
    try {
      console.log('📈 Buscando volume mensal de protocolos...');
      
      const volume = await transparencyService.getMonthlyVolume();
      
      res.json({
        success: true,
        data: volume,
        count: volume.length,
        timestamp: new Date().toISOString(),
        message: 'Volume mensal obtido com sucesso'
      });

    } catch (error: any) {
      console.error('❌ Erro ao obter volume mensal:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: 'Não foi possível obter o volume mensal'
      });
    }
  }

  /**
   * Obter assuntos mais populares
   * GET /api/public/protocols/subjects
   */
  async getPopularSubjects(req: Request, res: Response): Promise<void> {
    try {
      console.log('🏆 Buscando assuntos mais populares...');
      
      const subjects = await transparencyService.getPopularSubjects();
      
      res.json({
        success: true,
        data: subjects,
        count: subjects.length,
        timestamp: new Date().toISOString(),
        message: 'Assuntos populares obtidos com sucesso'
      });

    } catch (error: any) {
      console.error('❌ Erro ao obter assuntos populares:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: 'Não foi possível obter os assuntos populares'
      });
    }
  }

  /**
   * Consultar protocolo público
   * GET /api/public/protocol/:number
   */
  async getPublicProtocolInfo(req: Request, res: Response): Promise<void> {
    try {
      const { number } = req.params;
      
      if (!number || number.trim() === '') {
        res.status(400).json({
          success: false,
          error: 'Parâmetro inválido',
          message: 'Número do protocolo é obrigatório'
        });
        return;
      }

      console.log(`🔍 Consultando protocolo público: ${number}`);
      
      const protocolInfo = await transparencyService.getPublicProtocolInfo(number.trim());
      
      if (!protocolInfo) {
        res.status(404).json({
          success: false,
          error: 'Protocolo não encontrado',
          message: 'Não foi encontrado protocolo com este número'
        });
        return;
      }

      res.json({
        success: true,
        data: protocolInfo,
        timestamp: new Date().toISOString(),
        message: 'Protocolo encontrado com sucesso'
      });

    } catch (error: any) {
      console.error('❌ Erro ao consultar protocolo:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: 'Não foi possível consultar o protocolo'
      });
    }
  }

  /**
   * Obter dashboard completo de transparência
   * GET /api/public/dashboard
   */
  async getTransparencyDashboard(req: Request, res: Response): Promise<void> {
    try {
      console.log('🎯 Montando dashboard completo de transparência...');
      
      // Buscar todos os dados em paralelo para melhor performance
      const [
        generalStats,
        departmentStats,
        protocolStatus,
        monthlyVolume,
        popularSubjects
      ] = await Promise.all([
        transparencyService.getGeneralStats(),
        transparencyService.getDepartmentStats(),
        transparencyService.getProtocolStatusDistribution(),
        transparencyService.getMonthlyVolume(),
        transparencyService.getPopularSubjects()
      ]);

      const dashboard = {
        geral: generalStats,
        departamentos: departmentStats.slice(0, 10), // Top 10 departamentos
        statusProtocolos: protocolStatus,
        volumeMensal: monthlyVolume,
        assuntosPopulares: popularSubjects,
        ultimaAtualizacao: new Date().toISOString()
      };

      res.json({
        success: true,
        data: dashboard,
        timestamp: new Date().toISOString(),
        message: 'Dashboard de transparência montado com sucesso'
      });

    } catch (error: any) {
      console.error('❌ Erro ao montar dashboard de transparência:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: 'Não foi possível montar o dashboard de transparência'
      });
    }
  }

  /**
   * Health check para APIs públicas
   * GET /api/public/health
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      const isConnected = await transparencyService.testConnection();
      
      if (isConnected) {
        res.json({
          success: true,
          status: 'healthy',
          database: 'connected',
          timestamp: new Date().toISOString(),
          message: 'Sistema de transparência funcionando normalmente'
        });
      } else {
        res.status(503).json({
          success: false,
          status: 'unhealthy',
          database: 'disconnected',
          timestamp: new Date().toISOString(),
          message: 'Problema de conexão com o banco de dados'
        });
      }

    } catch (error: any) {
      console.error('❌ Erro no health check:', error);
      res.status(503).json({
        success: false,
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString(),
        message: 'Erro no sistema de transparência'
      });
    }
  }
}

export default new TransparencyController();