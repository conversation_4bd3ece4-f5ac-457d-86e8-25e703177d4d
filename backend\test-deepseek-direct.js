const axios = require('axios');
require('dotenv').config();

async function testDeepSeekAPI() {
  console.log('🔄 Testando DeepSeek API diretamente...');
  console.log('API Key:', process.env.DEEPSEEK_API_KEY ? 'Configurada ✅' : 'NÃO CONFIGURADA ❌');
  console.log('Base URL:', process.env.DEEPSEEK_API_BASE);
  
  try {
    const response = await axios.post(
      `${process.env.DEEPSEEK_API_BASE}/chat/completions`,
      {
        model: process.env.DEEPSEEK_MODEL || 'deepseek-chat',
        messages: [
          {
            role: 'user',
            content: 'Olá! Este é um teste de conexão. Responda apenas "API funcionando!"'
          }
        ],
        max_tokens: 100,
        temperature: 0.1
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );

    console.log('✅ API DeepSeek funcionando!');
    console.log('Resposta:', response.data.choices[0].message.content);
    console.log('Tokens usados:', response.data.usage);
    
  } catch (error) {
    console.error('❌ Erro na API DeepSeek:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else if (error.request) {
      console.error('Erro de rede:', error.message);
    } else {
      console.error('Erro:', error.message);
    }
  }
}

testDeepSeekAPI();