'use client';

import { useState, useEffect } from 'react';
import { FaUsers, FaBuilding, FaClock, FaComments, FaCheckCircle, FaTimesCircle } from 'react-icons/fa';

interface ExternalUser {
  id: number;
  nome: string;
  email: string;
  secretaria: string;
  cargo: string;
  external_user_id: string;
  first_access_date: string;
  created_at: string;
  conta_ativa: boolean;
  description: string;
}

export function ExternalUsers() {
  const [users, setUsers] = useState<ExternalUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadExternalUsers();
  }, []);

  const loadExternalUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('http://localhost:3001/api/system/external-users', {
        headers: {
          'X-Admin-Key': process.env.NEXT_PUBLIC_ADMIN_SECRET_KEY || 'admin_pv_valparaiso_2024_secure_key'
        }
      });

      const data = await response.json();
      
      if (data.success) {
        setUsers(data.data);
        console.log(`✅ ${data.total} usuários externos carregados`);
      } else {
        setError(data.error || 'Erro ao carregar usuários');
      }
    } catch (error) {
      console.error('❌ Erro ao carregar usuários externos:', error);
      setError('Erro de conexão com o servidor');
    } finally {
      setLoading(false);
    }
  };

  const groupBySecretaria = (users: ExternalUser[]) => {
    return users.reduce((acc, user) => {
      if (!acc[user.secretaria]) {
        acc[user.secretaria] = [];
      }
      acc[user.secretaria].push(user);
      return acc;
    }, {} as Record<string, ExternalUser[]>);
  };

  const secretarias = Object.keys(groupBySecretaria(users));
  const filteredUsers = filter === 'all' ? users : users.filter(u => u.secretaria === filter);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-4 bg-gray-200 dark:bg-gray-600 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-700 p-6">
        <div className="text-red-600 dark:text-red-400 text-center">
          <FaTimesCircle className="mx-auto mb-2 text-2xl" />
          <p className="font-medium">Erro ao carregar usuários externos</p>
          <p className="text-sm mt-1">{error}</p>
          <button
            onClick={loadExternalUsers}
            className="mt-3 px-4 py-2 bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 rounded-lg text-sm font-medium transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com filtros */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <FaUsers className="mr-2 text-blue-600" />
            Usuários do Sistema Prefeitura
          </h3>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Total: {users.length} usuários
          </div>
        </div>

        {/* Filtros */}
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
              filter === 'all'
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            Todos ({users.length})
          </button>
          {secretarias.map(secretaria => (
            <button
              key={secretaria}
              onClick={() => setFilter(secretaria)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                filter === secretaria
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {secretaria} ({groupBySecretaria(users)[secretaria].length})
            </button>
          ))}
        </div>

        {/* Lista de usuários */}
        <div className="space-y-3">
          {filteredUsers.map(user => (
            <div key={user.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex-1">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${user.conta_ativa ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {user.nome}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      {user.email} • {user.cargo}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center space-x-1">
                  <FaBuilding className="w-4 h-4" />
                  <span>{user.secretaria}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <FaClock className="w-4 h-4" />
                  <span>
                    {user.first_access_date 
                      ? formatDate(user.first_access_date)
                      : 'Nunca acessou'
                    }
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <FaComments className="w-4 h-4" />
                  <span>0 conversas</span> {/* TODO: implementar contagem */}
                </div>
                <div className={`flex items-center space-x-1 ${user.conta_ativa ? 'text-green-600' : 'text-red-600'}`}>
                  {user.conta_ativa ? <FaCheckCircle className="w-4 h-4" /> : <FaTimesCircle className="w-4 h-4" />}
                  <span>{user.conta_ativa ? 'Ativo' : 'Inativo'}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredUsers.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            {users.length === 0 ? (
              <div>
                <FaUsers className="mx-auto mb-2 text-3xl opacity-50" />
                <p>Nenhum usuário do sistema externo encontrado.</p>
                <p className="text-sm mt-1">Os usuários aparecerão aqui quando acessarem o chatbot via sistema da prefeitura.</p>
              </div>
            ) : (
              <p>Nenhum usuário encontrado para o filtro selecionado.</p>
            )}
          </div>
        )}
      </div>

      {/* Estatísticas por secretaria */}
      {secretarias.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {secretarias.map(secretaria => {
            const secretariaUsers = groupBySecretaria(users)[secretaria];
            const activeUsers = secretariaUsers.filter(u => u.conta_ativa).length;

            return (
              <div key={secretaria} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 dark:text-white">{secretaria}</h4>
                  <FaBuilding className="text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                  {secretariaUsers.length}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  {activeUsers} ativos • {secretariaUsers.length - activeUsers} inativos
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}