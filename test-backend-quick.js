#!/usr/bin/env node

/**
 * Teste rápido para verificar se o backend inicia sem erros
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Testando início do backend...\n');

const backendPath = path.join(__dirname, 'backend');
process.chdir(backendPath);

console.log('📁 Diretório:', process.cwd());

const child = spawn('npm', ['run', 'dev'], {
  stdio: 'pipe',
  shell: true
});

let output = '';
let errorOutput = '';

child.stdout.on('data', (data) => {
  const text = data.toString();
  output += text;
  console.log('📤 STDOUT:', text.trim());
  
  // Se ver a mensagem de servidor iniciado, parar o teste
  if (text.includes('Server running on port') || text.includes('🚀')) {
    console.log('\n✅ Backend iniciou com sucesso!');
    child.kill('SIGTERM');
    process.exit(0);
  }
});

child.stderr.on('data', (data) => {
  const text = data.toString();
  errorOutput += text;
  console.log('❌ STDERR:', text.trim());
  
  // Se houver erro crítico, parar
  if (text.includes('Error:') || text.includes('TypeError:')) {
    console.log('\n💥 Erro crítico detectado!');
    console.log('Saída completa do erro:');
    console.log(errorOutput);
    child.kill('SIGTERM');
    process.exit(1);
  }
});

child.on('close', (code) => {
  console.log(`\n🔚 Processo finalizado com código: ${code}`);
  
  if (code === 0) {
    console.log('✅ Teste concluído com sucesso!');
  } else {
    console.log('❌ Teste falhou!');
    console.log('Output completo:', output);
    console.log('Error completo:', errorOutput);
  }
});

// Timeout de 15 segundos
setTimeout(() => {
  console.log('\n⏰ Timeout atingido - parando teste...');
  child.kill('SIGTERM');
  
  if (output.length > 0) {
    console.log('✅ Backend parece estar funcionando (não houve erros críticos)');
    process.exit(0);
  } else {
    console.log('❌ Backend não iniciou dentro do tempo limite');
    process.exit(1);
  }
}, 15000);