import Image from 'next/image';

interface LogoProps {
  variant?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function Logo({ variant = 'horizontal', size = 'md', className = '' }: LogoProps) {
  const sizeClasses = {
    sm: 'h-8',
    md: 'h-12',
    lg: 'h-16'
  };

  const logoSrc = variant === 'horizontal' 
    ? '/images/logo/Sem fundo (1).png'
    : '/images/logo/PVAlinhada.png';

  return (
    <div className={`flex items-center ${className}`}>
      <Image
        src={logoSrc}
        alt="Prefeitura Virtual - Valparaíso de Goiás"
        width={variant === 'horizontal' ? 200 : 80}
        height={variant === 'horizontal' ? 60 : 120}
        className={`${sizeClasses[size]} w-auto object-contain`}
        priority
      />
    </div>
  );
}