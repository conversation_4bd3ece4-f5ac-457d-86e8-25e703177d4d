import { useState, useEffect, useCallback } from 'react';
import { api } from '@/services/api';

export interface Conversation {
  id: string;
  title: string;
  last_message: string | null;
  message_count: number;
  created_at: string;
  updated_at: string;
}

export function useConversations() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Carregar conversas
  const loadConversations = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.get('/conversations');
      setConversations(response.data.data || []);
    } catch (err: any) {
      console.error('Erro ao carregar conversas:', err);
      setError(err.response?.data?.error || 'Erro ao carregar conversas');
    } finally {
      setLoading(false);
    }
  }, []);

  // Criar nova conversa
  const createConversation = useCallback(async (title?: string) => {
    try {
      const response = await api.post('/conversations', { title });
      const newConversation = response.data.data;
      setConversations(prev => [newConversation, ...prev]);
      return newConversation;
    } catch (err: any) {
      console.error('Erro ao criar conversa:', err);
      throw new Error(err.response?.data?.error || 'Erro ao criar conversa');
    }
  }, []);

  // Deletar conversa
  const deleteConversation = useCallback(async (conversationId: string) => {
    try {
      await api.delete(`/conversations/${conversationId}`);
      setConversations(prev => prev.filter(c => c.id !== conversationId));
    } catch (err: any) {
      console.error('Erro ao deletar conversa:', err);
      throw new Error(err.response?.data?.error || 'Erro ao deletar conversa');
    }
  }, []);

  // Atualizar título da conversa
  const updateConversationTitle = useCallback(async (conversationId: string, title: string) => {
    try {
      await api.put(`/conversations/${conversationId}/title`, { title });
      setConversations(prev => 
        prev.map(c => c.id === conversationId ? { ...c, title } : c)
      );
    } catch (err: any) {
      console.error('Erro ao atualizar título:', err);
      throw new Error(err.response?.data?.error || 'Erro ao atualizar título');
    }
  }, []);

  // Buscar ou criar conversa ativa
  const getOrCreateActiveConversation = useCallback(async () => {
    try {
      const response = await api.get('/conversations/active');
      const conversation = response.data.data;
      
      // Adicionar à lista se não existir
      setConversations(prev => {
        const exists = prev.some(c => c.id === conversation.id);
        if (!exists) {
          return [conversation, ...prev];
        }
        return prev;
      });
      
      return conversation;
    } catch (err: any) {
      console.error('Erro ao buscar conversa ativa:', err);
      throw new Error(err.response?.data?.error || 'Erro ao buscar conversa ativa');
    }
  }, []);

  // Carregar conversas ao montar componente
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  return {
    conversations,
    loading,
    error,
    loadConversations,
    createConversation,
    deleteConversation,
    updateConversationTitle,
    getOrCreateActiveConversation
  };
}