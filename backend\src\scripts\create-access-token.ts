#!/usr/bin/env tsx

/**
 * Script para criar tokens de acesso permanentes
 * Uso: npm run create-token [description]
 */

import { accessTokenService } from '../services/accessTokenService';
import { postgresRepository } from '../repositories/PostgreSQLRepository';

async function createAccessToken() {
  console.log('🔐 Criando token de acesso para usuário autorizado...\n');

  try {
    // Garantir que as colunas necessárias existam
    console.log('📊 Verificando estrutura do banco de dados...');
    await postgresRepository.ensureAnonymousUserColumns();
    console.log('✅ Estrutura do banco OK\n');

    // Capturar descrição do argumento da linha de comando
    const description = process.argv[2] || `Usuário autorizado - ${new Date().toLocaleDateString('pt-BR')}`;

    // Criar token
    const tokenData = await accessTokenService.createAccessToken({
      description
    });

    // Gerar link de acesso
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const accessLink = accessTokenService.generateAccessLink(tokenData.token, baseUrl);

    // Exibir resultados
    console.log('✅ Token de acesso criado com sucesso!\n');
    console.log('📋 INFORMAÇÕES DO TOKEN:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`🆔 User ID: ${tokenData.userId}`);
    console.log(`📝 Descrição: ${tokenData.description}`);
    console.log(`📅 Criado em: ${tokenData.createdAt.toLocaleString('pt-BR')}`);
    console.log(`🔑 Token: ${tokenData.token}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

    console.log('🔗 LINK DE ACESSO:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(accessLink);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

    console.log('⚠️  IMPORTANTE:');
    console.log('• Este link permite acesso permanente ao chatbot');
    console.log('• As conversas ficam isoladas por usuário');
    console.log('• O usuário pode fechar e abrir o chatbot mantendo as conversas');
    console.log('• Para revogar o acesso, use o script revoke-token.ts\n');

    console.log('💡 EXEMPLO DE USO:');
    console.log(`curl -X POST http://localhost:3001/api/auth/validate-token \\`);
    console.log(`  -H "X-Access-Token: ${tokenData.token}" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -d '{"token":"${tokenData.token}"}'`);

  } catch (error) {
    console.error('❌ Erro ao criar token:', error);
    process.exit(1);
  }
}

// Executar apenas se chamado diretamente
if (require.main === module) {
  createAccessToken().then(() => {
    console.log('\n🎉 Processo concluído!');
    process.exit(0);
  }).catch((error) => {
    console.error('\n💥 Erro fatal:', error);
    process.exit(1);
  });
}