/**
 * Script para executar a criação das tabelas de conversas
 * Execute com: npm run db:create-conversations
 */

import { Client } from 'pg';
import fs from 'fs';
import path from 'path';
import chalk from 'chalk';

const postgresConfig = {
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso'
};

async function createConversationTables() {
  console.log(chalk.blue('\n=== CRIANDO TABELAS DE CONVERSAS ===\n'));
  
  const client = new Client(postgresConfig);
  
  try {
    await client.connect();
    console.log(chalk.green('✓ Conectado ao PostgreSQL'));
    
    // Ler o arquivo SQL
    const sqlPath = path.join(__dirname, 'create-conversation-tables.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    console.log(chalk.yellow('📝 Executando script SQL...'));
    
    // Executar o script
    await client.query(sqlContent);
    
    console.log(chalk.green('✓ Tabelas criadas com sucesso!'));
    
    // Verificar as tabelas criadas
    console.log(chalk.cyan('\n📊 Verificando estrutura criada:'));
    
    const tablesQuery = `
      SELECT table_name, 
             (SELECT COUNT(*) FROM information_schema.columns 
              WHERE columns.table_name = tables.table_name) as column_count
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('conversations', 'messages')
      ORDER BY table_name;
    `;
    
    const result = await client.query(tablesQuery);
    
    result.rows.forEach(row => {
      console.log(`  - Tabela "${row.table_name}": ${row.column_count} colunas`);
    });
    
    // Verificar índices
    const indexQuery = `
      SELECT tablename, indexname 
      FROM pg_indexes 
      WHERE tablename IN ('conversations', 'messages')
      ORDER BY tablename, indexname;
    `;
    
    const indexResult = await client.query(indexQuery);
    
    console.log(chalk.cyan('\n🔍 Índices criados:'));
    indexResult.rows.forEach(row => {
      console.log(`  - ${row.tablename}: ${row.indexname}`);
    });
    
    // Verificar triggers
    const triggerQuery = `
      SELECT trigger_name, event_object_table 
      FROM information_schema.triggers 
      WHERE event_object_table IN ('conversations', 'messages');
    `;
    
    const triggerResult = await client.query(triggerQuery);
    
    if (triggerResult.rows.length > 0) {
      console.log(chalk.cyan('\n⚡ Triggers criados:'));
      triggerResult.rows.forEach(row => {
        console.log(`  - ${row.event_object_table}: ${row.trigger_name}`);
      });
    }
    
    console.log(chalk.green('\n✅ Tabelas de conversas criadas e prontas para uso!'));
    
  } catch (error) {
    console.error(chalk.red('❌ Erro ao criar tabelas:'), error);
    throw error;
  } finally {
    await client.end();
  }
}

// Executar
createConversationTables()
  .then(() => {
    console.log(chalk.blue('\n=== PROCESSO CONCLUÍDO ===\n'));
    process.exit(0);
  })
  .catch((error) => {
    console.error(chalk.red('\n=== ERRO NO PROCESSO ===\n'), error);
    process.exit(1);
  });