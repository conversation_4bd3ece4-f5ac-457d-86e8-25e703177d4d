import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import rateLimit from 'express-rate-limit';

// Load environment variables
dotenv.config();

// Import routes
import authRoutes from './routes/authRoutes';
import chatRoutes from './routes/chatRoutes';
import dashboardRoutes from './routes/dashboardRoutes';
import conversationRoutes from './routes/conversationRoutes';
import postgresRoutes from './routes/postgresRoutes';
import tokenRoutes from './routes/tokenRoutes';
import externalRoutes from './routes/externalRoutes';
import transparencyRoutes from './routes/transparencyRoutes';

// Import Redis configuration
import { redisCache, redisQueue } from './config/redis';

const app = express();
const PORT = process.env['PORT'] || 3001;

// Initialize Redis connections with timeout and retry logic
async function initializeRedis() {
  const REDIS_TIMEOUT = 5000; // 5 seconds timeout
  
  try {
    console.log('🔄 Connecting to Redis...');
    
    // Create connection promises with timeout
    const cachePromise = Promise.race([
      redisCache.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Redis cache timeout')), REDIS_TIMEOUT)
      )
    ]);
    
    const queuePromise = Promise.race([
      redisQueue.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Redis queue timeout')), REDIS_TIMEOUT)
      )
    ]);
    
    await Promise.all([cachePromise, queuePromise]);
    console.log('✅ Redis connections initialized');
  } catch (error) {
    console.warn('⚠️  Redis connection failed, continuing without cache:', error.message);
    // Server continues without Redis - graceful degradation
  }
}

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Muitas requisições deste IP, tente novamente em 15 minutos.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env['FRONTEND_URL'] || 'http://localhost:3000',
  credentials: true,
}));

// Rate limiting
app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    // Check Redis health
    let redisStatus = 'disconnected';
    try {
      await redisCache.ping();
      redisStatus = 'connected';
    } catch (error) {
      redisStatus = 'error';
    }

    res.status(200).json({ 
      status: 'OK', 
      timestamp: new Date().toISOString(),
      service: 'chatbot-backend',
      version: '1.0.0',
      features: {
        cache: redisStatus,
        queue: redisStatus,
        discountSystem: 'active',
        smartUrgency: 'active',
      },
      environment: process.env['NODE_ENV'] || 'development',
    });
  } catch (error) {
    res.status(503).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      service: 'chatbot-backend',
      error: 'Service health check failed'
    });
  }
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/auth', tokenRoutes);
app.use('/api/admin', tokenRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/conversations', conversationRoutes);

// Public routes (no authentication required)
app.use('/api/public', transparencyRoutes);
app.use('/api/postgres', postgresRoutes);
app.use('/api/external', externalRoutes);
app.use('/api/system', require('./routes/systemRoutes').default);

// Root API endpoint
app.get('/api', (req, res) => {
  res.json({ 
    message: 'Chatbot API - Sistema de Secretarias',
    version: '1.0.0',
    features: [
      '🚀 Cache inteligente com 60-70% economia',
      '💰 Sistema de horários de desconto (50% off)',
      '🎯 Classificação automática de urgência',
      '📊 Métricas em tempo real',
      '⚡ Respostas instantâneas para consultas repetidas',
      '🔄 Sistema de filas para otimização de custos',
    ],
    endpoints: {
      chat: '/api/chat/message',
      queue: '/api/chat/queue/:userId',
      metrics: '/api/chat/metrics',
      health: '/api/chat/health',
      discount: '/api/chat/discount-info',
      dashboard: '/api/dashboard/full',
      dashboardMetrics: '/api/dashboard/metrics',
      savings: '/api/dashboard/savings',
      conversations: '/api/conversations/:userId',
      conversationMessages: '/api/conversations/:conversationId/messages',
      postgresHealth: '/api/postgres/health',
      postgresStats: '/api/postgres/estatisticas',
      postgresDashboard: '/api/postgres/dashboard',
      protocolos: '/api/postgres/protocolos/buscar',
      alvaras: '/api/postgres/protocolos/alvara',
      servicos: '/api/postgres/servicos',
    },
    discountSchedule: 'UTC 16:30-00:30 (50% economia)',
    estimatedMonthlyCost: 'R$ 270-1380 (média R$ 550)',
  });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('🚨 Server error:', err.stack);
  
  // Don't leak error details in production
  const isDevelopment = process.env['NODE_ENV'] === 'development';
  
  res.status(500).json({ 
    error: 'Erro interno do servidor',
    ...(isDevelopment && { details: err.message, stack: err.stack })
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: 'Endpoint não encontrado',
    availableEndpoints: [
      'GET /health',
      'GET /api',
      'POST /api/chat/message',
      'GET /api/chat/queue/:userId',
      'GET /api/chat/metrics',
      'GET /api/chat/health',
      'GET /api/chat/discount-info',
      'POST /api/chat/test-direct',
      'GET /api/chat/test-deepseek',
    ]
  });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🔄 SIGTERM received, shutting down gracefully...');
  
  try {
    // Close Redis connections
    await Promise.all([
      redisCache.disconnect(),
      redisQueue.disconnect(),
    ]);
    console.log('✅ Redis connections closed');
  } catch (error) {
    console.error('❌ Error closing Redis connections:', error);
  }
  
  process.exit(0);
});

// Start server
async function startServer() {
  try {
    // Initialize Redis first
    await initializeRedis();
    
    // Start HTTP server
    app.listen(PORT, () => {
      console.log(`🚀 Servidor rodando na porta ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/health`);
      console.log(`🎯 Chat API: http://localhost:${PORT}/api/chat/message`);
      console.log(`💰 Sistema de cache e desconto ativo!`);
      console.log(`⚡ Economia esperada: 60-70% com cache + horários de desconto`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

export default app;