import { Client } from 'pg';
import { config } from 'dotenv';

config();

const postgresConfig = {
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso'
};

async function verifyRealStructure() {
  console.log('🔍 VERIFICANDO ESTRUTURA REAL DOS DADOS');
  console.log('======================================\n');
  
  const client = new Client(postgresConfig);
  
  try {
    await client.connect();
    
    // 1. Verificar estrutura da tabela protocolo_virtual_interessados
    console.log('1️⃣ Analisando tabela protocolo_virtual_interessados...');
    const interessadosColumns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'protocolo_virtual_interessados'
      ORDER BY ordinal_position
    `);
    
    if (interessadosColumns.rows.length > 0) {
      console.log('Colunas encontradas:');
      interessadosColumns.rows.forEach(col => {
        console.log(`   - ${col.column_name} (${col.data_type})`);
      });
      
      // Verificar dados de exemplo
      const interessadosSample = await client.query(`
        SELECT * FROM protocolo_virtual_interessados LIMIT 1
      `);
      
      if (interessadosSample.rows.length > 0) {
        console.log('\nExemplo de dados:');
        console.log(interessadosSample.rows[0]);
      }
    }
    
    // 2. Verificar estrutura da tabela pessoas
    console.log('\n2️⃣ Analisando tabela pessoas...');
    const pessoasColumns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'pessoas'
      ORDER BY ordinal_position
    `);
    
    if (pessoasColumns.rows.length > 0) {
      console.log('Colunas encontradas:');
      pessoasColumns.rows.forEach(col => {
        console.log(`   - ${col.column_name} (${col.data_type})`);
      });
      
      // Verificar dados de exemplo
      const pessoasSample = await client.query(`
        SELECT * FROM pessoas LIMIT 1
      `);
      
      if (pessoasSample.rows.length > 0) {
        console.log('\nExemplo de dados:');
        console.log(pessoasSample.rows[0]);
      }
    }
    
    // 3. Verificar como os protocolos se relacionam com interessados
    console.log('\n3️⃣ Verificando relacionamento protocolo -> interessado...');
    const protocoloComInteressado = await client.query(`
      SELECT 
        p.id,
        p.id_protocolo,
        p.id_interessado,
        p.data_protocolo,
        p.observacao,
        a.descricao as assunto,
        s.descricao as situacao
      FROM protocolo_virtual_processos p
      LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
      LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
      WHERE p.id_interessado IS NOT NULL
      LIMIT 5
    `);
    
    console.log(`Encontrados ${protocoloComInteressado.rows.length} protocolos com interessados:`);
    protocoloComInteressado.rows.forEach((row, index) => {
      console.log(`\n${index + 1}. Protocolo ${row.id_protocolo}:`);
      console.log(`   - ID Interessado: ${row.id_interessado}`);
      console.log(`   - Assunto: ${row.assunto}`);
      console.log(`   - Situação: ${row.situacao}`);
    });
    
    // 4. Verificar estrutura de pessoa_fisicas e pessoa_juridicas
    console.log('\n4️⃣ Analisando tabelas pessoa_fisicas e pessoa_juridicas...');
    
    // Pessoa física
    const pfColumns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'pessoa_fisicas'
        AND column_name IN ('id', 'cpf', 'rg', 'nome', 'id_pessoa')
      ORDER BY ordinal_position
    `);
    
    if (pfColumns.rows.length > 0) {
      console.log('\nColunas em pessoa_fisicas:');
      pfColumns.rows.forEach(col => {
        console.log(`   - ${col.column_name} (${col.data_type})`);
      });
    }
    
    // Pessoa jurídica
    const pjColumns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'pessoa_juridicas'
        AND column_name IN ('id', 'cnpj', 'razao_social', 'nome_fantasia', 'id_pessoa')
      ORDER BY ordinal_position
    `);
    
    if (pjColumns.rows.length > 0) {
      console.log('\nColunas em pessoa_juridicas:');
      pjColumns.rows.forEach(col => {
        console.log(`   - ${col.column_name} (${col.data_type})`);
      });
    }
    
    // 5. Tentar fazer um JOIN completo para entender a estrutura
    console.log('\n5️⃣ Testando JOIN completo para buscar dados de interessados...');
    const joinTest = await client.query(`
      SELECT 
        p.id_protocolo,
        p.data_protocolo,
        p.observacao,
        i.nome as interessado_nome,
        i.cpf_cnpj as interessado_documento,
        pe.nome as pessoa_nome,
        pf.cpf,
        pj.cnpj,
        pj.razao_social
      FROM protocolo_virtual_processos p
      LEFT JOIN protocolo_virtual_interessados i ON p.id_interessado = i.id
      LEFT JOIN pessoas pe ON i.id_pessoa = pe.id
      LEFT JOIN pessoa_fisicas pf ON pe.id = pf.id_pessoa
      LEFT JOIN pessoa_juridicas pj ON pe.id = pj.id_pessoa
      WHERE p.id_protocolo IS NOT NULL
      LIMIT 5
    `);
    
    console.log(`\nResultados do JOIN (${joinTest.rows.length} registros):`);
    joinTest.rows.forEach((row, index) => {
      console.log(`\n${index + 1}. Protocolo ${row.id_protocolo}:`);
      console.log(`   - Nome Interessado: ${row.interessado_nome || row.pessoa_nome || 'N/A'}`);
      console.log(`   - Documento: ${row.interessado_documento || row.cpf || row.cnpj || 'N/A'}`);
      if (row.razao_social) console.log(`   - Razão Social: ${row.razao_social}`);
    });
    
  } catch (error: any) {
    console.error('❌ Erro:', error.message);
    console.error('Detalhes:', error);
  } finally {
    await client.end();
  }
  
  console.log('\n✅ Verificação concluída!');
}

// Executar verificação
verifyRealStructure().catch(console.error);