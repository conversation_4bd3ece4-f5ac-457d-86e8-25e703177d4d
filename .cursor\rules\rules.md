---
alwaysApply: true
---
# Regras Pessoais de Desenvolvimento

Estas são minhas preferências e padrões pessoais de desenvolvimento que aplico
em todos os projetos. O Cursor deve seguir estas diretrizes como meu estilo
pessoal de código.

### SEMPRE FALAR EM PT-BR

## 1. Preferências de Código

### 1.1. TypeScript/JavaScript

- **TypeScript obrigatório** em todos os projetos
- Configuração strict sempre ativada
- Evitar `any` - sempre tipar adequadamente
- Preferir interfaces sobre types quando possível
- Usar ES6+ (arrow functions, destructuring, template literals)
- Comentários em português para lógica complexa

### 1.2. React (Minhas Preferências)

- **Componentes funcionais** sempre (nunca classes)
- **React Query/TanStack Query** para estado servidor
- **Custom hooks** para lógica reutilizável
- Componentes pequenos (max. 300 linhas)
- Props sempre tipadas
- Context API modular (por domínio, não global)

### 1.3. Banco de Dados

- **PostgreSQL** como banco principal
  - ✅ Criação de tabelas e migrações: scripts SQL versionados ou ferramenta (Knex, Sequelize, dbmate)
  - ✅ Consultas: biblioteca `pg` com **SQL parametrizado** (placeholder `$1`, `$2`, ...)
  - ✅ Triggers para `updated_at`, histórico, etc.
  - ✅ RLS (Row Level Security) habilitado onde necessário
  - ✅ Índices e views para consultas frequentes
  - ❌ **NUNCA concatenar strings SQL** (sempre parâmetros)
- Nomenclatura snake_case para tabelas/colunas
- PKs sempre `id`, FKs como `{tabela}_id`
- Normalização 3NF por padrão

### 1.4. Edge Functions/Serverless (Opcional)

- Estrutura consistente com CORS
- Validação de entrada sempre (Zod ou equivalente)
- Tratamento robusto de erros
- Logs estruturados para debugging
- Resposta padronizada JSON
- Plataforma livre (ex.: Cloudflare Workers, Vercel Functions) – **sem dependência de Supabase**

## 2. Padrões de Arquitetura Pessoais

### 2.1. Organização de Código

```
src/
├── components/
│   ├── ui/           # Design system
│   ├── layouts/      # Templates
│   └── [dominio]/    # Por área de negócio
├── hooks/            # Lógica reutilizável  
├── pages/            # Rotas específicas
├── services/         # Integração externa
├── types/            # Definições TypeScript
└── utils/            # Funções puras
```

### 2.2. Padrões de Componente

```typescript
// Minha estrutura preferida
const MeuComponente: React.FC<Props> = ({ prop1, prop2 }) => {
    // 1. Hooks de estado
    // 2. Queries/Mutations
    // 3. Event handlers
    // 4. JSX return
};

export default MeuComponente;
```

### 2.3. Hooks Customizados

```typescript
// Sempre retornar objeto, não array
const useMinhaLogica = () => {
    return {
        data,
        loading,
        error,
        actions: { create, update, delete },
    };
};
```

## 3. Segurança e Performance (Minhas Regras)

### 3.1. Segurança

- **Validação dupla:** frontend + backend
- Sanitização de inputs sempre
- Secrets nunca em código
- Auditoria para ações sensíveis
- Rate limiting em APIs

### 3.2. Performance

- **Bundle splitting** por padrão
- **Lazy loading** para rotas pesadas
- **Memoization** consciente (useMemo, useCallback)
- Paginação para listas grandes
- Cache estratégico

### 3.3. UX/UI

- **Responsividade obrigatória**
- Loading states sempre
- Error boundaries
- Acessibilidade básica (WCAG)
- Feedback visual para ações

## 4. Fluxo de Desenvolvimento Pessoal

### 4.1. Desenvolvimento

- **TDD quando possível**
- Commits pequenos e descritivos
- Branch por feature
- Code review antes de merge
- Documentação atualizada

### 4.2. Testes

- Testes unitários para lógica complexa
- Testes de integração para fluxos críticos
- E2E para user journeys principais
- Coverage mínimo 70%

### 4.3. Deploy

- Build sem warnings
- Testes passando
- Performance verificada
- Rollback preparado

## 5. Preferências de Ferramentas

### 5.1. Frontend

- **Vite** para build tool
- **shadcn/ui** para componentes
- **Tailwind CSS** para styling
- **React Query** para estado servidor
- **React Hook Form + Zod** para formulários

### 5.2. Backend/Database

- **Node.js + Express** como stack principal
- **PostgreSQL** gerenciado (RDS, DigitalOcean, etc.)
- **Redis** para cache e filas Bull
- **Row Level Security** sempre
- Serverless opcional em Workers/Vercel – sem vínculo a Supabase

### 5.3. Testes

- **Vitest** para unit tests
- **Playwright** para E2E
- **Testing Library** para React
- **MSW** para mocking APIs

## 6. Padrões de Código Específicos

### 6.1. Nomenclatura

- **Componentes:** PascalCase
- **Hooks:** camelCase com 'use' prefix
- **Funções:** camelCase descritivo
- **Constantes:** UPPER_SNAKE_CASE
- **Arquivos:** kebab-case ou PascalCase

### 6.2. Imports

```typescript
// Ordem preferida:
// 1. React
import React from "react";
// 2. External libraries
import { useQuery } from "@tanstack/react-query";
// 3. Internal components/hooks
import { Button } from "@/components/ui/button";
// 4. Types
import type { MyType } from "@/types";
```

### 6.3. Error Handling

```typescript
// Padrão para tratamento de erros
try {
    const result = await operation();
    return { data: result, error: null };
} catch (error) {
    console.error("Operation failed:", error);
    return { data: null, error: (error as Error).message };
}
```

## 7. Checklist Pessoal

### 7.1. Antes de Commit

- [ ] TypeScript sem erros
- [ ] Lint/format passou
- [ ] Não há console.logs esquecidos
- [ ] Componentes tipados
- [ ] Performance considerada

### 7.2. Antes de PR

- [ ] Testes passando
- [ ] Build funcionando
- [ ] Documentação atualizada
- [ ] Breaking changes documentadas
- [ ] Acessibilidade verificada

### 7.3. Code Review

- [ ] Lógica clara e simples
- [ ] Reutilização de código
- [ ] Segurança verificada (SQL parametrizado, token)
- [ ] Performance aceitável
- [ ] Padrões seguidos

---

_Estas são minhas regras pessoais de desenvolvimento_

_Aplique estas preferências em qualquer projeto que eu trabalhe_

_Para regras específicas do projeto atual, consulte project-rules.mdc_

_Versão: 3.1.0 – PostgreSQL Integrado (JULHO 2025)_