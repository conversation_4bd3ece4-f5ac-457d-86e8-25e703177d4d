import { useState, useCallback } from 'react';
import { ChatMessage, ApiResponse } from '@/types';

interface ChatState {
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  conversationId: string | null;
  copiedMessageId: string | null;
}

interface SendMessageParams {
  message: string;
  conversationId?: string;
}

interface SendMessageResponse {
  success: boolean;
  response?: string;
  data?: {
    response: string;
    source: string;
    cost?: number;
    timestamp: string;
  };
  metadata?: {
    processingTime: number;
    urgency: string;
    discountActive: boolean;
    tokens?: {
      input: number;
      output: number;
      total: number;
    };
  };
  error?: string;
}

export function useChat(accessToken?: string, user?: any) {
  const [state, setState] = useState<ChatState>({
    messages: [],
    isLoading: false,
    error: null,
    conversationId: null,
    copiedMessageId: null
  });

  const sendMessage = useCallback(async ({ message, conversationId }: SendMessageParams) => {
    if (!message.trim()) return;

    console.log('🔍 Debug useChat:', { accessToken, user, hasToken: !!accessToken });

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }));

    // Adicionar mensagem do usuário imediatamente
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      conversationId: conversationId || 'temp',
      userId: user?.id || 'user',
      content: message,
      type: 'user',
      timestamp: new Date()
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage]
    }));

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Adicionar token de acesso se disponível
      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
        // Manter X-Access-Token temporariamente para compatibilidade
        headers['X-Access-Token'] = accessToken;
        console.log('✅ Token adicionado ao header');
      } else {
        console.warn('⚠️ Token não disponível!');
      }

      const response = await fetch('http://localhost:3001/api/chat/message', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          message,
          userId: user?.id && !isNaN(Number(user.id)) ? Number(user.id) : 'anonymous',
          secretaria: 'administracao',
          conversationId: conversationId || undefined
        })
      });

      const data: SendMessageResponse = await response.json();

      if (data.success && data.data?.response) {
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          conversationId: conversationId || 'temp',
          userId: 'assistant',
          content: data.data.response,
          type: 'assistant',
          timestamp: new Date(),
          metadata: {
            responseTime: data.metadata?.processingTime,
            tokensUsed: data.metadata?.tokens?.total,
            cost: data.data.cost
          }
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, assistantMessage],
          isLoading: false,
          conversationId: conversationId || prev.conversationId || 'temp'
        }));

        return data;
      } else {
        throw new Error(data.error || 'Erro ao enviar mensagem');
      }
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      }));
      throw error;
    }
  }, [accessToken, user]);

  const clearMessages = useCallback(() => {
    setState({
      messages: [],
      isLoading: false,
      error: null,
      conversationId: null,
      copiedMessageId: null
    });
  }, []);

  const handleCopyMessage = useCallback((content: string, id: string) => {
    navigator.clipboard.writeText(content);
    setState(prev => ({ ...prev, copiedMessageId: id }));
    
    // Reset after 2 seconds
    setTimeout(() => {
      setState(prev => ({ ...prev, copiedMessageId: null }));
    }, 2000);
  }, []);

  const setConversationId = useCallback((id: string) => {
    setState(prev => ({
      ...prev,
      conversationId: id
    }));
  }, []);

  const loadConversationMessages = useCallback(async (conversationId: string) => {
    if (!accessToken) return;

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }));

    try {
      const response = await fetch(`http://localhost:3001/api/conversations/${conversationId}/messages`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
          'X-Access-Token': accessToken // Manter temporariamente para compatibilidade
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao carregar mensagens');
      }

      const data = await response.json();
      
      if (data.success && data.data) {
        const chatMessages: ChatMessage[] = data.data.map((msg: any) => ({
          id: msg.id,
          content: msg.content,
          role: msg.role as 'user' | 'assistant',
          timestamp: new Date(msg.created_at)
        }));

        setState(prev => ({
          ...prev,
          messages: chatMessages,
          conversationId,
          isLoading: false
        }));
      }
    } catch (error) {
      console.error('Erro ao carregar mensagens:', error);
      setState(prev => ({
        ...prev,
        error: 'Erro ao carregar mensagens da conversa',
        isLoading: false
      }));
    }
  }, [accessToken]);

  // Wrapper simples para sendMessage
  const sendMessageSimple = useCallback((message: string) => {
    return sendMessage({ message, conversationId: state.conversationId || undefined });
  }, [sendMessage, state.conversationId]);

  return {
    messages: state.messages,
    isLoading: state.isLoading,
    error: state.error,
    conversationId: state.conversationId,
    copiedMessageId: state.copiedMessageId,
    sendMessage: sendMessageSimple,
    clearMessages,
    handleCopyMessage,
    setConversationId,
    loadConversationMessages
  };
}