{"name": "chatbot-inteligente-secretarias", "version": "1.0.0", "description": "Sistema de Chatbot Inteligente para Secretarias da Prefeitura de Valparaíso de Goiás", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install"}, "devDependencies": {"concurrently": "^8.2.2", "tsx": "^4.20.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"@henkey/postgres-mcp-server": "^1.0.5"}}