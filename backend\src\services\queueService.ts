import Bull, { Queue, Job } from 'bull';
import { redisQueue, isDiscountTime, getNextDiscountTime } from '../config/redis';
import { urgencyClassifier, UrgencyLevel } from './urgencyClassifier';
import { MessageContext } from './cacheService';

// Tipos para o sistema de filas
export interface QueuedMessage {
  id: string;
  message: string;
  context: MessageContext;
  urgency: UrgencyLevel;
  queuedAt: Date;
  estimatedProcessTime: Date;
  estimatedSavings: number;
}

export interface QueueStatus {
  totalQueued: number;
  processingNow: number;
  nextDiscountTime: Date;
  isDiscountActive: boolean;
  estimatedWaitTime: number; // em minutos
}

export interface ProcessingResult {
  success: boolean;
  response?: string;
  error?: string;
  cost: number;
  processedAt: Date;
}

class QueueService {
  private messageQueue: Queue;
  private discountQueue: Queue;
  
  constructor() {
    // Fila para mensagens normais (processamento imediato)
    this.messageQueue = new Bull('message processing', {
      redis: {
        host: redisQueue.options.host,
        port: redisQueue.options.port,
        password: redisQueue.options.password,
      },
      defaultJobOptions: {
        removeOnComplete: 10, // Manter apenas 10 jobs completos
        removeOnFail: 5,      // Manter apenas 5 jobs com falha
        attempts: 3,          // Tentar 3 vezes em caso de falha
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    // Fila para mensagens que esperam desconto
    this.discountQueue = new Bull('discount processing', {
      redis: {
        host: redisQueue.options.host,
        port: redisQueue.options.port,
        password: redisQueue.options.password,
      },
      defaultJobOptions: {
        removeOnComplete: 20,
        removeOnFail: 10,
        attempts: 2,
        backoff: {
          type: 'fixed',
          delay: 30000, // 30 segundos
        },
      },
    });

    this.setupProcessors();
    this.setupScheduledProcessing();
  }

  // Configurar processors para as filas
  private setupProcessors(): void {
    // Processor para mensagens imediatas
    this.messageQueue.process('immediate', 5, async (job: Job) => {
      const { message, context } = job.data;
      console.log(`🚀 Processando mensagem imediata: ${message.substring(0, 50)}...`);
      
      // Aqui seria chamada a API do DeepSeek
      // Por ora, retorna resposta simulada
      return await this.processWithDeepSeek(message, context, false);
    });

    // Processor para mensagens com desconto
    this.discountQueue.process('discount', 10, async (job: Job) => {
      const { message, context } = job.data;
      console.log(`💰 Processando mensagem com desconto: ${message.substring(0, 50)}...`);
      
      // Processar apenas se estiver em horário de desconto
      if (!isDiscountTime()) {
        // Re-agendar para próximo horário de desconto
        const nextDiscount = getNextDiscountTime();
        const delay = nextDiscount.getTime() - Date.now();
        
        await this.discountQueue.add('discount', job.data, {
          delay: Math.max(delay, 60000), // Mínimo 1 minuto
        });
        
        throw new Error('Reagendado para horário de desconto');
      }
      
      return await this.processWithDeepSeek(message, context, true);
    });

    // Event listeners
    this.messageQueue.on('completed', (job, result) => {
      console.log(`✅ Mensagem ${job.id} processada com sucesso`);
    });

    this.messageQueue.on('failed', (job, err) => {
      console.error(`❌ Mensagem ${job.id} falhou:`, err.message);
    });

    this.discountQueue.on('completed', (job, result) => {
      console.log(`💰 Mensagem ${job.id} processada com desconto`);
    });
  }

  // Configurar processamento agendado para horários de desconto
  private setupScheduledProcessing(): void {
    // Verificar a cada 15 minutos se há mensagens para processar no desconto
    setInterval(async () => {
      if (isDiscountTime()) {
        const waiting = await this.discountQueue.getWaiting();
        console.log(`🕐 Horário de desconto ativo. ${waiting.length} mensagens na fila.`);
        
        // Processar mensagens em lote durante o desconto
        for (const job of waiting.slice(0, 20)) { // Máximo 20 por vez
          await job.promote();
        }
      }
    }, 15 * 60 * 1000); // 15 minutos
  }

  // Adicionar mensagem à fila apropriada
  async queueMessage(
    message: string, 
    context: MessageContext,
    forceImmediate: boolean = false
  ): Promise<QueuedMessage> {
    const urgencyResult = urgencyClassifier.classify(message, {
      userRole: context.userId.includes('admin') ? 'admin' : 'operador', // Simplificado
      secretaria: context.secretaria,
    });

    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date();
    
    // Determinar se deve processar imediatamente ou aguardar desconto
    const shouldProcessImmediately = 
      forceImmediate || 
      urgencyResult.level === 'immediate' || 
      isDiscountTime();

    let estimatedProcessTime: Date;
    let estimatedSavings = 0;

    if (shouldProcessImmediately) {
      // Processar agora
      const job = await this.messageQueue.add('immediate', {
        id: messageId,
        message,
        context,
        queuedAt: now,
      }, {
        priority: urgencyResult.level === 'immediate' ? 100 : 50,
      });

      estimatedProcessTime = new Date(now.getTime() + 30000); // 30 segundos
    } else {
      // Aguardar desconto
      const nextDiscount = getNextDiscountTime();
      const delay = Math.max(nextDiscount.getTime() - now.getTime(), 60000);
      
      const job = await this.discountQueue.add('discount', {
        id: messageId,
        message,
        context,
        queuedAt: now,
      }, {
        delay,
        priority: urgencyResult.level === 'batch' ? 10 : 30,
      });

      estimatedProcessTime = nextDiscount;
      estimatedSavings = 0.00219 * 0.5; // 50% de desconto
    }

    return {
      id: messageId,
      message,
      context,
      urgency: urgencyResult.level,
      queuedAt: now,
      estimatedProcessTime,
      estimatedSavings,
    };
  }

  // Obter status da fila
  async getQueueStatus(): Promise<QueueStatus> {
    const [immediateWaiting, immediateActive, discountWaiting, discountActive] = await Promise.all([
      this.messageQueue.getWaiting(),
      this.messageQueue.getActive(),
      this.discountQueue.getWaiting(),
      this.discountQueue.getActive(),
    ]);

    const totalQueued = immediateWaiting.length + discountWaiting.length;
    const processingNow = immediateActive.length + discountActive.length;
    const nextDiscountTime = getNextDiscountTime();
    const isDiscountActive = isDiscountTime();
    
    // Calcular tempo estimado de espera
    const avgProcessingTime = 30; // 30 segundos por mensagem
    const estimatedWaitTime = isDiscountActive ? 
      (totalQueued * avgProcessingTime) / 60 : // em minutos
      Math.max((nextDiscountTime.getTime() - Date.now()) / 60000, 0);

    return {
      totalQueued,
      processingNow,
      nextDiscountTime,
      isDiscountActive,
      estimatedWaitTime,
    };
  }

  // Obter mensagens na fila de um usuário específico
  async getUserQueuedMessages(userId: string): Promise<QueuedMessage[]> {
    const [immediateJobs, discountJobs] = await Promise.all([
      this.messageQueue.getJobs(['waiting', 'delayed']),
      this.discountQueue.getJobs(['waiting', 'delayed']),
    ]);

    const allJobs = [...immediateJobs, ...discountJobs];
    const userJobs = allJobs.filter(job => job.data.context.userId === userId);

    return userJobs.map(job => ({
      id: job.data.id,
      message: job.data.message,
      context: job.data.context,
      urgency: urgencyClassifier.classify(job.data.message).level,
      queuedAt: new Date(job.data.queuedAt),
      estimatedProcessTime: new Date(job.processedOn || job.opts.delay || Date.now()),
      estimatedSavings: job.opts.delay ? 0.00219 * 0.5 : 0,
    }));
  }

  // Cancelar mensagem na fila
  async cancelQueuedMessage(messageId: string, userId: string): Promise<boolean> {
    const [immediateJobs, discountJobs] = await Promise.all([
      this.messageQueue.getJobs(['waiting', 'delayed']),
      this.discountQueue.getJobs(['waiting', 'delayed']),
    ]);

    const allJobs = [...immediateJobs, ...discountJobs];
    const job = allJobs.find(j => 
      j.data.id === messageId && 
      j.data.context.userId === userId
    );

    if (job) {
      await job.remove();
      return true;
    }

    return false;
  }

  // Promover mensagem para processamento imediato
  async promoteToImmediate(messageId: string, userId: string): Promise<boolean> {
    const discountJobs = await this.discountQueue.getJobs(['waiting', 'delayed']);
    const job = discountJobs.find(j => 
      j.data.id === messageId && 
      j.data.context.userId === userId
    );

    if (job) {
      // Remove da fila de desconto
      await job.remove();
      
      // Adiciona na fila imediata
      await this.messageQueue.add('immediate', job.data, {
        priority: 75, // Prioridade alta para promoções
      });
      
      return true;
    }

    return false;
  }

  // Simular processamento com DeepSeek (placeholder)
  private async processWithDeepSeek(
    message: string, 
    context: MessageContext, 
    withDiscount: boolean
  ): Promise<ProcessingResult> {
    try {
      // Simular tempo de processamento
      const processingTime = Math.random() * 2000 + 1000; // 1-3 segundos
      await new Promise(resolve => setTimeout(resolve, processingTime));

      // Simular custo
      const baseCost = 0.00219;
      const cost = withDiscount ? baseCost * 0.5 : baseCost;

      // Simular resposta
      const response = `Resposta simulada para: "${message.substring(0, 50)}..." 
                       Processado ${withDiscount ? 'com desconto' : 'em horário normal'} 
                       para a secretaria de ${context.secretaria}.`;

      return {
        success: true,
        response,
        cost,
        processedAt: new Date(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        cost: 0,
        processedAt: new Date(),
      };
    }
  }

  // Estatísticas da fila
  async getQueueStats(): Promise<{
    totalProcessed: number;
    totalSavings: number;
    avgProcessingTime: number;
    discountUsageRate: number;
  }> {
    const [immediateCompleted, discountCompleted] = await Promise.all([
      this.messageQueue.getCompleted(),
      this.discountQueue.getCompleted(),
    ]);

    const totalProcessed = immediateCompleted.length + discountCompleted.length;
    const totalSavings = discountCompleted.length * 0.00219 * 0.5;
    const discountUsageRate = totalProcessed > 0 ? 
      (discountCompleted.length / totalProcessed) * 100 : 0;

    return {
      totalProcessed,
      totalSavings,
      avgProcessingTime: 2.5, // 2.5 segundos média
      discountUsageRate,
    };
  }

  // Limpar filas (para manutenção)
  async clearQueues(): Promise<void> {
    await Promise.all([
      this.messageQueue.clean(0, 'completed'),
      this.messageQueue.clean(0, 'failed'),
      this.discountQueue.clean(0, 'completed'),
      this.discountQueue.clean(0, 'failed'),
    ]);
  }

  // Fechar conexões (para shutdown graceful)
  async close(): Promise<void> {
    await Promise.all([
      this.messageQueue.close(),
      this.discountQueue.close(),
    ]);
  }
}

export const queueService = new QueueService();
export default queueService;