/**
 * Rotas para integração com sistema da prefeitura
 * Permite que o sistema da prefeitura gere tokens de acesso para usuários
 */

import { Router } from 'express';
import { z } from 'zod';
import { externalController } from '../controllers/externalController';

const router = Router();

// Schema de validação para requisição do sistema prefeitura
const generateTokenSchema = z.object({
  userId: z.string().min(1, 'ID do usuário é obrigatório'),
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: z.string().email('Email inválido'),
  secretaria: z.string().min(2, 'Secretaria é obrigatória'),
  cargo: z.string().optional(),
  systemKey: z.string().min(10, 'Chave do sistema inválida')
});

/**
 * @route POST /api/external/generate-or-get-token
 * @desc Gerar ou retornar token existente para usuário do sistema prefeitura
 * @access External System Only
 */
router.post('/generate-or-get-token', async (req, res) => {
  try {
    console.log('🔌 Requisição de token externo recebida:', {
      userId: req.body.userId,
      name: req.body.name,
      email: req.body.email,
      secretaria: req.body.secretaria
    });

    // Validar dados da requisição
    const validatedData = generateTokenSchema.parse(req.body);
    
    // Validar chave do sistema
    if (validatedData.systemKey !== process.env.PREFEITURA_SYSTEM_KEY) {
      console.warn('❌ Tentativa de acesso com chave inválida');
      return res.status(403).json({
        success: false,
        error: 'Chave de sistema inválida'
      });
    }

    // Processar requisição
    const result = await externalController.generateOrGetToken(validatedData);
    
    console.log('✅ Token processado:', {
      userId: validatedData.userId,
      isFirstAccess: result.isFirstAccess
    });

    res.json({
      success: true,
      chatbotUrl: result.chatbotUrl,
      isFirstAccess: result.isFirstAccess,
      message: result.isFirstAccess ? 'Token criado com sucesso' : 'Token existente retornado'
    });

  } catch (error: any) {
    if (error instanceof z.ZodError) {
      console.error('❌ Dados inválidos na requisição:', error.errors);
      return res.status(400).json({
        success: false,
        error: 'Dados inválidos',
        details: error.errors
      });
    }

    console.error('❌ Erro ao gerar/buscar token:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno no servidor'
    });
  }
});

/**
 * @route GET /api/external/user-stats/:userId
 * @desc Buscar estatísticas de uso do usuário
 * @access External System Only
 */
router.get('/user-stats/:userId', async (req, res) => {
  try {
    const systemKey = req.headers['x-system-key'] as string;
    
    if (systemKey !== process.env.PREFEITURA_SYSTEM_KEY) {
      console.warn('❌ Tentativa de acesso a stats com chave inválida');
      return res.status(403).json({
        success: false,
        error: 'Chave de sistema inválida'
      });
    }

    const stats = await externalController.getUserStats(req.params.userId);
    
    if (!stats) {
      return res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('❌ Erro ao buscar estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar estatísticas'
    });
  }
});

/**
 * @route GET /api/external/health
 * @desc Endpoint para verificar saúde da integração
 * @access External System Only
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'external-integration',
    version: '1.0.0'
  });
});

export default router;