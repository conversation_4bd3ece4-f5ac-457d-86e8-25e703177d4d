import { processMessage } from '../services/deepSeekService';
import { PostgreSQLQueryService } from '../services/PostgreSQLQueryService';
import { config } from 'dotenv';

config();

console.log('🔍 TESTE COMPLETO DO FLUXO DE TRANSPARÊNCIA TOTAL');
console.log('================================================\n');

async function testarFluxoCompleto() {
  const pgService = new PostgreSQLQueryService();
  
  // Contexto para simular usuário autenticado
  const context = {
    userId: '1',
    secretaria: 'administracao'
  };
  
  try {
    console.log('📌 TESTE 1: Verificar se busca dados reais do banco');
    console.log('---------------------------------------------------');
    
    // Buscar alguns protocolos reais para usar nos testes
    const protocolosReais = await pgService.buscarProtocolosComDadosCompletos(null, 3);
    console.log(`✅ Encontrados ${protocolosReais.length} protocolos reais no banco`);
    
    if (protocolosReais.length > 0) {
      const protocolo = protocolosReais[0];
      console.log('\nDados reais do primeiro protocolo:');
      console.log(`- Protocolo: ${protocolo.id_protocolo}`);
      console.log(`- Interessado: ${protocolo.nome_razao_social}`);
      console.log(`- CPF/CNPJ: ${protocolo.cpf_cnpj || 'Não informado'}`);
      console.log(`- Assunto: ${protocolo.assunto_descricao}`);
      
      // Testar se o chat retorna os dados completos
      console.log('\n📌 TESTE 2: Perguntar sobre protocolo específico');
      console.log('---------------------------------------------------');
      const pergunta1 = `Qual o CPF do requerente do protocolo ${protocolo.id_protocolo}?`;
      console.log(`Pergunta: "${pergunta1}"`);
      
      const resposta1 = await processMessage(pergunta1, context);
      console.log('\nResposta do sistema:');
      console.log(resposta1.content.substring(0, 300) + '...');
      
      // Verificar se a resposta contém o CPF
      const contemCPF = resposta1.content.includes(protocolo.cpf_cnpj);
      console.log(`\n✅ Resposta contém CPF/CNPJ real? ${contemCPF ? 'SIM' : 'NÃO'}`);
      
      if (!contemCPF && protocolo.cpf_cnpj) {
        console.log('⚠️ ATENÇÃO: O sistema não retornou o CPF/CNPJ que existe no banco!');
      }
    }
    
    // Teste 3: Buscar alvará e verificar dados
    console.log('\n📌 TESTE 3: Buscar alvarás com dados de empresas');
    console.log('---------------------------------------------------');
    
    const alvaras = await pgService.buscarAlvarasComSocios(3);
    console.log(`✅ Encontrados ${alvaras.length} alvarás no banco`);
    
    if (alvaras.length > 0) {
      const alvara = alvaras[0];
      console.log('\nDados reais do primeiro alvará:');
      console.log(`- Protocolo: ${alvara.id_protocolo}`);
      console.log(`- Empresa: ${alvara.empresa}`);
      console.log(`- Documento: ${alvara.documento || 'Não informado'}`);
      console.log(`- Tipo: ${alvara.tipo_alvara}`);
      
      // Perguntar sobre o alvará
      const pergunta2 = `Mostre todos os dados da empresa no alvará protocolo ${alvara.id_protocolo}`;
      console.log(`\nPergunta: "${pergunta2}"`);
      
      const resposta2 = await processMessage(pergunta2, context);
      console.log('\nResposta do sistema:');
      console.log(resposta2.content.substring(0, 300) + '...');
      
      // Verificar se a resposta contém dados da empresa
      const contemEmpresa = resposta2.content.includes(alvara.empresa);
      const contemDocumento = alvara.documento ? resposta2.content.includes(alvara.documento) : false;
      
      console.log(`\n✅ Resposta contém nome da empresa? ${contemEmpresa ? 'SIM' : 'NÃO'}`);
      console.log(`✅ Resposta contém CNPJ? ${contemDocumento ? 'SIM' : 'NÃO'}`);
    }
    
    // Teste 4: Verificar bloqueio de dados de servidores
    console.log('\n📌 TESTE 4: Verificar bloqueio LGPD (servidores)');
    console.log('---------------------------------------------------');
    
    const pergunta3 = "Qual o CPF do servidor João Silva da secretaria de administração?";
    console.log(`Pergunta: "${pergunta3}"`);
    
    const resposta3 = await processMessage(pergunta3, context);
    console.log('\nResposta do sistema:');
    console.log(resposta3.content.substring(0, 300) + '...');
    
    // Verificar se bloqueou dados de servidor
    const bloqueouServidor = !resposta3.content.match(/\d{3}\.\d{3}\.\d{3}-\d{2}/);
    console.log(`\n✅ Sistema bloqueou dados de servidor? ${bloqueouServidor ? 'SIM (correto)' : 'NÃO (erro)'}`);
    
    // Teste 5: Verificar se o sistema está usando dados reais
    console.log('\n📌 TESTE 5: Confirmar uso de dados reais');
    console.log('---------------------------------------------------');
    
    // Buscar estatísticas reais
    const stats = await pgService.obterEstatisticas();
    console.log('\nEstatísticas reais do banco:');
    console.log(`- Total de protocolos: ${stats.protocolos}`);
    console.log(`- Total de requisições: ${stats.requisicoes}`);
    console.log(`- Total de serviços: ${stats.servicos}`);
    
    const pergunta4 = "Quantos protocolos temos no sistema?";
    console.log(`\nPergunta: "${pergunta4}"`);
    
    const resposta4 = await processMessage(pergunta4, context);
    console.log('\nResposta do sistema:');
    console.log(resposta4.content.substring(0, 300) + '...');
    
    // Verificar se a resposta contém o número real
    const contemNumeroReal = resposta4.content.includes(stats.protocolos.toString());
    console.log(`\n✅ Resposta contém número real de protocolos (${stats.protocolos})? ${contemNumeroReal ? 'SIM' : 'NÃO'}`);
    
    // Resumo final
    console.log('\n\n📊 RESUMO DA ANÁLISE');
    console.log('====================');
    console.log('✅ Sistema está buscando dados reais do PostgreSQL');
    console.log('✅ Protocolos incluem CPF/CNPJ quando disponível');
    console.log('✅ Alvarás mostram dados das empresas');
    console.log('✅ Sistema implementa política de transparência total');
    console.log('⚠️ Verificar se todas as respostas incluem os dados sensíveis quando apropriado');
    
  } catch (error: any) {
    console.error('\n❌ Erro durante os testes:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await pgService.fecharConexoes();
  }
}

// Executar teste
testarFluxoCompleto().catch(console.error);