import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { clsx } from 'clsx';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { FaPlus, FaKey, FaCopy, FaTrash, FaSpinner, FaCheck, FaEye, FaEyeSlash } from 'react-icons/fa';

interface TokenData {
  token: string;
  userId: number;
  description: string;
  isActive: boolean;
  createdAt: string;
  accessLink: string;
}

interface TokenManagerProps {
  className?: string;
}

export function TokenManager({ className }: TokenManagerProps) {
  const {
    createToken,
    listTokens,
    revokeToken,
    loading,
    error,
    clearError
  } = useAdminAuth();

  const [tokens, setTokens] = useState<TokenData[]>([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [description, setDescription] = useState('');
  const [copiedToken, setCopiedToken] = useState<string | null>(null);
  const [showFullTokens, setShowFullTokens] = useState<Record<string, boolean>>({});

  // Carregar tokens ao montar
  useEffect(() => {
    loadTokens();
  }, []);

  const loadTokens = async () => {
    const tokenList = await listTokens();
    setTokens(tokenList);
  };

  const handleCreateToken = async () => {
    const newToken = await createToken({
      description: description.trim() || `Usuário autorizado - ${new Date().toLocaleDateString('pt-BR')}`
    });

    if (newToken) {
      setTokens(prev => [newToken, ...prev]);
      setDescription('');
      setShowCreateForm(false);
      
      // Auto-copiar o link do novo token
      await copyToClipboard(newToken.accessLink, `link-${newToken.token}`);
    }
  };

  const handleRevokeToken = async (token: string) => {
    if (window.confirm('Tem certeza que deseja revogar este token? O usuário perderá acesso ao chatbot.')) {
      const success = await revokeToken(token);
      if (success) {
        await loadTokens(); // Recarregar lista
      }
    }
  };

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedToken(id);
      setTimeout(() => setCopiedToken(null), 2000);
    } catch (err) {
      console.error('Erro ao copiar:', err);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR');
  };

  const toggleTokenVisibility = (token: string) => {
    setShowFullTokens(prev => ({
      ...prev,
      [token]: !prev[token]
    }));
  };

  const maskToken = (token: string) => {
    if (showFullTokens[token]) {
      return token;
    }
    return `${token.substring(0, 8)}...${token.substring(token.length - 4)}`;
  };

  return (
    <Card className={clsx('', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <FaKey className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <span>Gestão de Tokens de Acesso</span>
            <span className="text-sm text-gray-500 dark:text-gray-400">({tokens.length})</span>
          </CardTitle>

          <button
            onClick={() => setShowCreateForm(!showCreateForm)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <FaPlus className="w-4 h-4" />
            <span>Novo Token</span>
          </button>
        </div>

        {error && (
          <div className="mt-2 p-3 bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded-lg">
            <div className="flex items-center justify-between">
              <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
              <button
                onClick={clearError}
                className="text-red-500 hover:text-red-700 dark:hover:text-red-300"
              >
                ×
              </button>
            </div>
          </div>
        )}

        {showCreateForm && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-900 dark:text-white mb-3">Criar Novo Token</h3>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Descrição (opcional)
                </label>
                <input
                  type="text"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Ex: Token para João Silva - Secretaria de Saúde"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={handleCreateToken}
                  disabled={loading}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                >
                  {loading ? <FaSpinner className="w-4 h-4 animate-spin" /> : <FaKey className="w-4 h-4" />}
                  <span>Criar Token</span>
                </button>
                
                <button
                  onClick={() => {
                    setShowCreateForm(false);
                    setDescription('');
                  }}
                  className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
                >
                  Cancelar
                </button>
              </div>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {loading && tokens.length === 0 ? (
          <div className="text-center py-8">
            <FaSpinner className="w-8 h-8 animate-spin text-blue-600 dark:text-blue-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-300">Carregando tokens...</p>
          </div>
        ) : tokens.length === 0 ? (
          <div className="text-center py-8">
            <FaKey className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Nenhum token criado
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Crie um novo token para dar acesso ao chatbot para os usuários
            </p>
            <button
              onClick={() => setShowCreateForm(true)}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <FaPlus className="w-4 h-4" />
              <span>Criar Primeiro Token</span>
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {tokens.map((token) => (
              <div
                key={token.token}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Header */}
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={clsx(
                        'inline-flex px-2 py-1 text-xs font-medium rounded-full',
                        token.isActive 
                          ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                          : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
                      )}>
                        {token.isActive ? 'Ativo' : 'Revogado'}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        ID: {token.userId}
                      </span>
                    </div>

                    {/* Descrição */}
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                      {token.description}
                    </h4>

                    {/* Token */}
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Token:</span>
                      <code className="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono">
                        {maskToken(token.token)}
                      </code>
                      <button
                        onClick={() => toggleTokenVisibility(token.token)}
                        className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                        title={showFullTokens[token.token] ? 'Ocultar token' : 'Mostrar token completo'}
                      >
                        {showFullTokens[token.token] ? <FaEyeSlash className="w-4 h-4" /> : <FaEye className="w-4 h-4" />}
                      </button>
                    </div>

                    {/* Link de acesso */}
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Link:</span>
                      <code className="text-sm bg-blue-100 dark:bg-blue-900 px-2 py-1 rounded font-mono flex-1 truncate">
                        {token.accessLink}
                      </code>
                    </div>

                    {/* Data de criação */}
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Criado em: {formatDate(token.createdAt)}
                    </p>
                  </div>

                  {/* Ações */}
                  <div className="flex flex-col space-y-2 ml-4">
                    <button
                      onClick={() => copyToClipboard(token.accessLink, `link-${token.token}`)}
                      className="flex items-center space-x-1 px-3 py-1 bg-blue-100 dark:bg-blue-900 hover:bg-blue-200 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded text-sm transition-colors"
                      title="Copiar link de acesso"
                    >
                      {copiedToken === `link-${token.token}` ? (
                        <>
                          <FaCheck className="w-3 h-3" />
                          <span>Copiado!</span>
                        </>
                      ) : (
                        <>
                          <FaCopy className="w-3 h-3" />
                          <span>Copiar Link</span>
                        </>
                      )}
                    </button>

                    <button
                      onClick={() => copyToClipboard(token.token, `token-${token.token}`)}
                      className="flex items-center space-x-1 px-3 py-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm transition-colors"
                      title="Copiar token"
                    >
                      {copiedToken === `token-${token.token}` ? (
                        <>
                          <FaCheck className="w-3 h-3" />
                          <span>Copiado!</span>
                        </>
                      ) : (
                        <>
                          <FaCopy className="w-3 h-3" />
                          <span>Copiar Token</span>
                        </>
                      )}
                    </button>

                    {token.isActive && (
                      <button
                        onClick={() => handleRevokeToken(token.token)}
                        className="flex items-center space-x-1 px-3 py-1 bg-red-100 dark:bg-red-900 hover:bg-red-200 dark:hover:bg-red-800 text-red-700 dark:text-red-300 rounded text-sm transition-colors"
                        title="Revogar acesso"
                      >
                        <FaTrash className="w-3 h-3" />
                        <span>Revogar</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}