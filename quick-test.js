// Teste rápido de conexão Redis
const Redis = require('ioredis');

async function quickTest() {
  console.log('🔍 Testando conexão Redis...');
  
  const redis = new Redis({
    host: 'localhost',
    port: 6379,
    lazyConnect: true,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3
  });

  try {
    await redis.ping();
    console.log('✅ Redis conectado com sucesso!');
    console.log('🚀 Você pode executar: npm run dev');
  } catch (error) {
    console.log('❌ Redis não conectado:', error.message);
    console.log('');
    console.log('🔧 VERIFIQUE:');
    console.log('1. Redis está rodando? (redis-server.exe)');
    console.log('2. Porta 6379 está livre?');
    console.log('3. Firewall não está bloqueando?');
  } finally {
    redis.disconnect();
  }
}

quickTest();