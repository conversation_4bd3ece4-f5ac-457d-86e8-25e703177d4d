# 🔗 Integração Sistema Prefeitura + Chatbot - G<PERSON><PERSON>

## 📋 Resumo da Implementação

A integração entre o sistema da prefeitura e o chatbot foi **implementada com sucesso** e está funcionando. Este documento descreve como utilizar a integração.

## ✅ Status da Implementação

### Backend (Concluído)
- ✅ Migration do banco PostgreSQL executada
- ✅ Endpoint `/api/external/generate-or-get-token` criado
- ✅ Controller externo implementado
- ✅ Serviços atualizados para usuários externos
- ✅ Variáveis de ambiente configuradas

### Frontend (Concluído)
- ✅ Componente `ExternalUsers` criado
- ✅ Nova aba "Usuários Sistema" no admin dashboard
- ✅ Interface completa para monitoramento

### Testes (Concluído)
- ✅ Script de teste completo criado
- ✅ Validação end-to-end implementada

## 🚀 Como Usar a Integração

### 1. Para o Sistema da Prefeitura

Adicione este código JavaScript no seu sistema:

```javascript
/**
 * Função para integrar com o chatbot
 * Chame esta função quando o usuário clicar no botão "Acessar Chatbot"
 */
async function acessarChatbot(usuario) {
  // Mostrar loading
  const loadingElement = document.getElementById('chatbot-loading');
  if (loadingElement) loadingElement.style.display = 'block';
  
  try {
    const response = await fetch('http://localhost:3001/api/external/generate-or-get-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId: usuario.id.toString(),
        name: usuario.nome,
        email: usuario.email,
        secretaria: usuario.secretaria,
        cargo: usuario.cargo || '',
        systemKey: 'prefeitura_valparaiso_2024_integration_key_secure'
      })
    });

    const data = await response.json();

    if (data.success) {
      // Abrir chatbot em nova aba
      const chatWindow = window.open(data.chatbotUrl, '_blank', 'width=1200,height=800');
      
      if (data.isFirstAccess) {
        alert('Primeiro acesso ao chatbot configurado com sucesso!');
      }
      
      if (chatWindow) {
        chatWindow.focus();
      }
    } else {
      alert('Erro ao acessar chatbot: ' + data.error);
    }
  } catch (error) {
    console.error('Erro ao conectar com chatbot:', error);
    alert('Erro de conexão com o chatbot. Tente novamente.');
  } finally {
    // Esconder loading
    if (loadingElement) loadingElement.style.display = 'none';
  }
}
```

### 2. HTML do Botão

Adicione este HTML na tela do usuário logado:

```html
<div id="chatbot-access" style="margin: 20px 0;">
  <button 
    onclick="acessarChatbot(usuarioLogado)" 
    class="btn btn-primary"
    style="background: #2563eb; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
    🤖 Acessar Chatbot IA
  </button>
  <div id="chatbot-loading" style="display: none; margin-top: 10px; color: #666;">
    Configurando acesso ao chatbot...
  </div>
</div>
```

### 3. Objeto Usuario Esperado

O objeto `usuarioLogado` deve ter esta estrutura:

```javascript
const usuarioLogado = {
  id: "123",                              // ID único no sistema
  nome: "João Silva",                     // Nome completo
  email: "<EMAIL>",    // Email válido
  secretaria: "Saúde",                    // Secretaria/Departamento
  cargo: "Enfermeiro"                     // Cargo (opcional)
};
```

## 🔧 Testando a Integração

### 1. Executar Script de Teste

```bash
# No diretório backend
npx tsx src/scripts/test-external-integration.ts
```

### 2. Teste Manual

**Endpoint:** `POST http://localhost:3001/api/external/generate-or-get-token`

**Body:**
```json
{
  "userId": "123",
  "name": "João Silva",
  "email": "<EMAIL>",
  "secretaria": "Saúde",
  "cargo": "Enfermeiro",
  "systemKey": "prefeitura_valparaiso_2024_integration_key_secure"
}
```

**Resposta esperada:**
```json
{
  "success": true,
  "chatbotUrl": "http://localhost:3000/chat?token=ABC123...",
  "isFirstAccess": true,
  "message": "Token criado com sucesso"
}
```

## 👑 Monitoramento Admin

### 1. Acessar Dashboard

Acesse: `http://localhost:3000/admin`

### 2. Nova Aba "Usuários Sistema"

- Lista todos os usuários que acessaram via sistema da prefeitura
- Filtros por secretaria
- Estatísticas de uso
- Status ativo/inativo

## 🔒 Segurança

### 1. Chave de Sistema

```env
PREFEITURA_SYSTEM_KEY=prefeitura_valparaiso_2024_integration_key_secure
```

### 2. Validações Implementadas

- ✅ Validação da chave do sistema
- ✅ Validação de dados com Zod
- ✅ Rate limiting
- ✅ Logs de segurança
- ✅ Tokens únicos e seguros

## 🌐 Configuração para Produção

### 1. Alterar URLs

No código JavaScript, substitua:
```javascript
// De:
'http://localhost:3001/api/external/generate-or-get-token'

// Para:
'https://chatbot.prefeitura.com/api/external/generate-or-get-token'
```

### 2. Configurar CORS

Se necessário, adicione o domínio da prefeitura no CORS do backend.

### 3. HTTPS Obrigatório

Em produção, use sempre HTTPS para ambos os sistemas.

## 📊 Fluxo Completo

```mermaid
sequenceDiagram
    participant U as Usuário
    participant SP as Sistema Prefeitura
    participant CB as Chatbot Backend
    participant CF as Chatbot Frontend
    
    U->>SP: Login no sistema
    U->>SP: Clica "Acessar Chatbot"
    
    SP->>CB: POST /api/external/generate-or-get-token
    CB->>CB: Verifica se usuário existe
    
    alt Primeiro acesso
        CB->>CB: Cria novo token
        CB->>SP: Retorna URL + isFirstAccess: true
    else Usuário existente
        CB->>SP: Retorna URL + isFirstAccess: false
    end
    
    SP->>CF: Abre nova aba com token
    CF->>CF: Usuário utiliza chatbot
```

## 🔍 Endpoints Disponíveis

### 1. Gerar/Obter Token
- **URL:** `POST /api/external/generate-or-get-token`
- **Auth:** System Key
- **Uso:** Sistema da prefeitura

### 2. Estatísticas do Usuário
- **URL:** `GET /api/external/user-stats/:userId`
- **Auth:** System Key (header)
- **Uso:** Sistema da prefeitura

### 3. Listar Usuários (Admin)
- **URL:** `GET /api/system/external-users`
- **Auth:** Admin Key
- **Uso:** Dashboard admin

### 4. Health Check
- **URL:** `GET /api/external/health`
- **Auth:** Nenhuma
- **Uso:** Monitoramento

## 🐛 Troubleshooting

### Erro: "Chave de sistema inválida"
- Verificar `PREFEITURA_SYSTEM_KEY` no `.env`
- Conferir se a chave no JavaScript está correta

### Erro: "Dados inválidos"
- Verificar estrutura do objeto `usuario`
- Conferir se email é válido
- Verificar se todos os campos obrigatórios estão presentes

### Chatbot não abre
- Verificar se o frontend está rodando na porta 3000
- Conferir se o popup não foi bloqueado pelo navegador
- Verificar console do navegador para erros

### Usuários não aparecem no admin
- Verificar se a migration foi executada
- Conferir se o endpoint admin está funcionando
- Verificar chave admin no frontend

## 📞 Suporte

Para problemas ou dúvidas:

1. Verificar logs do backend
2. Executar script de teste
3. Conferir documentação dos endpoints
4. Verificar configurações de ambiente

---

**🎉 A integração está pronta para uso!**

Agora os usuários da prefeitura podem acessar o chatbot de forma transparente e segura, com controle total pelo dashboard administrativo.