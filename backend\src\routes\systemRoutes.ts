import { Router } from 'express';
import systemController from '../controllers/systemController';
import { authenticate } from '../middleware/authenticateMiddleware';
import { externalController } from '../controllers/externalController';

const router = Router();

// Rotas protegidas (requerem autenticação admin)
// Para o sistema admin, vamos usar uma verificação mais simples por enquanto
const adminCheck = (req: any, res: any, next: any) => {
  const adminKey = req.headers['x-admin-key'];
  if (adminKey === process.env.ADMIN_SECRET_KEY) {
    next();
  } else {
    res.status(403).json({ success: false, error: 'Ace<PERSON> negado' });
  }
};

router.use(adminCheck);

// Status do sistema
router.get('/status', systemController.getSystemStatus.bind(systemController));

// Logs do sistema
router.get('/logs', systemController.getSystemLogs.bind(systemController));

/**
 * @route GET /api/system/external-users
 * @desc Listar usuários do sistema externo (apenas para admin)
 * @access Admin
 */
router.get('/external-users', async (req, res) => {
  try {
    console.log('📋 Solicitação de lista de usuários externos pelo admin');
    
    const users = await externalController.listExternalUsers();

    res.json({
      success: true,
      data: users,
      total: users.length,
      message: `${users.length} usuários externos encontrados`
    });

  } catch (error: any) {
    console.error('❌ Erro ao listar usuários externos:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao listar usuários externos',
      details: error.message
    });
  }
});

export default router;