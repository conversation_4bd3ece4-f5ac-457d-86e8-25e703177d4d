'use client';
import { useState, useRef, KeyboardEvent } from 'react';
import { FaPaperPlane, FaSpinner, FaTrash, FaChartBar, FaBolt } from 'react-icons/fa';

interface Props {
  onSend: (message: string) => void;
  isLoading: boolean;
  messageCount?: number;
  onClear?: () => void;
}

export default function InputBar({ onSend, isLoading, messageCount = 0, onClear }: Props) {
  const [input, setInput] = useState('');
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      onSend(input.trim());
      setInput('');
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };

  const handleClear = () => {
    if (onClear && window.confirm('Limpar toda a conversa?')) {
      onClear();
    }
  };

  return (
    <div className="border-t border-gray-200 dark:border-brand/20 bg-white dark:bg-brand-dark">
      <div className="max-w-4xl mx-auto px-4 py-4">
        <form onSubmit={handleSubmit} className="flex gap-4">
          <div className="flex-1 relative">
            <textarea
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Digite sua pergunta..."
              rows={1}
              className="w-full px-4 py-3 bg-gray-100 dark:bg-white/10 border border-gray-200 dark:border-brand/20 rounded-2xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-brand focus:border-transparent transition-all"
              style={{
                minHeight: '48px',
                maxHeight: '120px',
                height: 'auto'
              }}
            />
            {input.length > 0 && (
              <div className="absolute right-2 bottom-2 text-xs text-gray-500 dark:text-gray-500">
                {input.length} caracteres
              </div>
            )}
          </div>

          <div className="flex gap-2">
            {messageCount > 0 && onClear && (
              <button
                type="button"
                onClick={handleClear}
                aria-label="Limpar conversa"
                className="p-3 bg-red-500/20 hover:bg-red-500/30 rounded-xl text-red-400 transition-colors"
              >
                <FaTrash />
              </button>
            )}

            <button
              type="submit"
              disabled={!input.trim() || isLoading}
              className={`px-6 py-3 rounded-xl font-medium transition-all flex items-center gap-2 ${
                input.trim() && !isLoading
                  ? 'bg-brand text-white hover:bg-brand-600'
                  : 'bg-gray-200 dark:bg-gray-800 text-gray-400 dark:text-gray-500 cursor-not-allowed'
              }`}
            >
              {isLoading ? (
                <>
                  <FaSpinner className="animate-spin" />
                  <span>Enviando...</span>
                </>
              ) : (
                <>
                  <FaPaperPlane />
                  <span>Enviar</span>
                </>
              )}
            </button>
          </div>
        </form>

        {/* Info Bar */}
        <div className="flex items-center justify-between mt-3 text-xs text-gray-500 dark:text-gray-500">
          <div className="flex items-center gap-4">
            <span>Pressione Enter para enviar • Shift+Enter para nova linha</span>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <FaChartBar className="text-gray-400 dark:text-gray-600" />
              <span>{messageCount} mensagens</span>
            </div>
            <div className="flex items-center gap-1">
              <FaBolt className="text-yellow-600" />
              <span>Cache ativo</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}