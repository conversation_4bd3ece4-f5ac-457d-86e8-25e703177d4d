/**
 * Controller para integração com sistema externo da prefeitura
 * Gerencia tokens de acesso para usuários do sistema da prefeitura
 */

import { accessTokenService } from '../services/accessTokenService';
import { postgresRepository } from '../repositories/PostgreSQLRepository';

interface ExternalUserData {
  userId: string;
  name: string;
  email: string;
  secretaria: string;
  cargo?: string;
}

interface TokenResult {
  chatbotUrl: string;
  isFirstAccess: boolean;
  tokenData?: any;
}

export class ExternalController {
  /**
   * Gerar ou retornar token existente para usuário externo
   */
  async generateOrGetToken(userData: ExternalUserData): Promise<TokenResult> {
    try {
      console.log('🔍 Verificando usuário externo:', userData.userId);

      // 1. Verificar se usuário já existe (por external_user_id)
      const existingUser = await postgresRepository.findUserByExternalId(userData.userId);
      
      if (existingUser && existingUser.access_token && existingUser.conta_ativa) {
        // Usuário já tem token ativo - retornar existing
        console.log('✅ Usuário existente encontrado:', existingUser.id);
        
        const chatbotUrl = this.generateChatbotUrl(existingUser.access_token);
        
        return {
          chatbotUrl,
          isFirstAccess: false,
          tokenData: {
            userId: existingUser.id,
            token: existingUser.access_token,
            createdAt: existingUser.created_at
          }
        };
      } else {
        // Primeira vez ou token inativo - criar novo
        console.log('🆕 Criando novo token para usuário externo');
        
        const tokenData = await accessTokenService.createExternalUserToken({
          externalUserId: userData.userId,
          name: userData.name,
          email: userData.email,
          secretaria: userData.secretaria,
          cargo: userData.cargo
        });
        
        const chatbotUrl = this.generateChatbotUrl(tokenData.token);
        
        console.log('✅ Token criado com sucesso:', tokenData.userId);
        
        return {
          chatbotUrl,
          isFirstAccess: true,
          tokenData
        };
      }
    } catch (error: any) {
      console.error('❌ Erro ao gerar/buscar token externo:', error);
      throw new Error(`Falha ao processar solicitação: ${error.message}`);
    }
  }

  /**
   * Buscar estatísticas de uso do usuário
   */
  async getUserStats(userId: string) {
    try {
      console.log('📊 Buscando estatísticas para usuário:', userId);
      
      const user = await postgresRepository.findUserByExternalId(userId);
      
      if (!user) {
        console.log('❌ Usuário não encontrado:', userId);
        return null;
      }

      // Buscar estatísticas de conversas (se implementado)
      const stats = {
        userId: user.id,
        externalUserId: user.external_user_id,
        name: user.nome,
        email: user.email,
        secretaria: user.secretaria,
        cargo: user.cargo,
        firstAccess: user.first_access_date,
        lastAccess: user.updated_at,
        isActive: user.conta_ativa,
        totalConversations: 0, // TODO: implementar contagem
        totalMessages: 0 // TODO: implementar contagem
      };

      console.log('✅ Estatísticas encontradas para:', user.nome);
      return stats;
    } catch (error: any) {
      console.error('❌ Erro ao buscar estatísticas do usuário:', error);
      throw error;
    }
  }

  /**
   * Gerar URL completa do chatbot com token
   */
  private generateChatbotUrl(token: string): string {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    return `${baseUrl}/chat?token=${encodeURIComponent(token)}`;
  }

  /**
   * Validar integridade da requisição externa
   */
  validateExternalRequest(systemKey: string): boolean {
    const validKey = process.env.PREFEITURA_SYSTEM_KEY;
    
    if (!validKey) {
      console.error('❌ PREFEITURA_SYSTEM_KEY não configurada');
      return false;
    }

    if (!systemKey || systemKey !== validKey) {
      console.warn('❌ Chave de sistema inválida fornecida');
      return false;
    }

    return true;
  }

  /**
   * Listar todos os usuários externos (para admin)
   */
  async listExternalUsers(): Promise<any[]> {
    try {
      console.log('📋 Listando usuários externos');
      
      const users = await postgresRepository.listExternalUsers();
      
      console.log(`✅ ${users.length} usuários externos encontrados`);
      return users;
    } catch (error: any) {
      console.error('❌ Erro ao listar usuários externos:', error);
      throw error;
    }
  }

  /**
   * Desativar usuário externo
   */
  async deactivateExternalUser(externalUserId: string): Promise<boolean> {
    try {
      console.log('🔒 Desativando usuário externo:', externalUserId);
      
      const user = await postgresRepository.findUserByExternalId(externalUserId);
      
      if (!user) {
        console.log('❌ Usuário não encontrado para desativação');
        return false;
      }

      const result = await postgresRepository.deactivateUserByToken(user.access_token);
      
      if (result) {
        console.log('✅ Usuário externo desativado com sucesso');
      }
      
      return result;
    } catch (error: any) {
      console.error('❌ Erro ao desativar usuário externo:', error);
      return false;
    }
  }
}

// Singleton instance
export const externalController = new ExternalController();