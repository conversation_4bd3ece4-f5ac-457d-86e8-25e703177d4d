import { useState, useEffect, useCallback } from 'react';

interface AdminUser {
  isAuthenticated: boolean;
  adminKey: string;
}

interface TokenData {
  token: string;
  userId: number;
  description: string;
  isActive: boolean;
  createdAt: string;
  accessLink: string;
}

interface CreateTokenOptions {
  description?: string;
  customToken?: string;
}

export function useAdminAuth() {
  const [adminUser, setAdminUser] = useState<AdminUser>({
    isAuthenticated: false,
    adminKey: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const API_BASE = process.env.NODE_ENV === 'production' 
    ? '/api' 
    : 'http://localhost:3001/api';

  // Verificar se já está autenticado ao carregar
  useEffect(() => {
    const savedKey = localStorage.getItem('admin_secret_key');
    if (savedKey) {
      setAdminUser({
        isAuthenticated: true,
        adminKey: savedKey
      });
    }
  }, []);

  /**
   * Fazer login admin com chave secreta
   */
  const login = useCallback(async (adminKey: string): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      // Testar a chave fazendo uma requisição para listar tokens
      const response = await fetch(`${API_BASE}/admin/tokens`, {
        headers: {
          'X-Admin-Key': adminKey,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        // Chave válida - salvar e autenticar
        localStorage.setItem('admin_secret_key', adminKey);
        setAdminUser({
          isAuthenticated: true,
          adminKey
        });
        return true;
      } else {
        setError('Chave de administrador inválida');
        return false;
      }
    } catch (err) {
      setError('Erro ao conectar com o servidor');
      return false;
    } finally {
      setLoading(false);
    }
  }, [API_BASE]);

  /**
   * Fazer logout admin
   */
  const logout = useCallback(() => {
    localStorage.removeItem('admin_secret_key');
    setAdminUser({
      isAuthenticated: false,
      adminKey: ''
    });
    setError(null);
  }, []);

  /**
   * Criar novo token de acesso
   */
  const createToken = useCallback(async (options: CreateTokenOptions = {}): Promise<TokenData | null> => {
    if (!adminUser.isAuthenticated) {
      setError('Usuário não autenticado');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE}/admin/create-token`, {
        method: 'POST',
        headers: {
          'X-Admin-Key': adminUser.adminKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(options)
      });

      const data = await response.json();

      if (data.success) {
        return data.data;
      } else {
        setError(data.error || 'Erro ao criar token');
        return null;
      }
    } catch (err) {
      setError('Erro ao criar token');
      return null;
    } finally {
      setLoading(false);
    }
  }, [adminUser, API_BASE]);

  /**
   * Listar todos os tokens
   */
  const listTokens = useCallback(async (): Promise<TokenData[]> => {
    if (!adminUser.isAuthenticated) {
      return [];
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE}/admin/tokens`, {
        headers: {
          'X-Admin-Key': adminUser.adminKey,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.success) {
        return data.data;
      } else {
        setError(data.error || 'Erro ao listar tokens');
        return [];
      }
    } catch (err) {
      setError('Erro ao listar tokens');
      return [];
    } finally {
      setLoading(false);
    }
  }, [adminUser, API_BASE]);

  /**
   * Revogar token
   */
  const revokeToken = useCallback(async (token: string): Promise<boolean> => {
    if (!adminUser.isAuthenticated) {
      setError('Usuário não autenticado');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE}/admin/tokens/${token}`, {
        method: 'DELETE',
        headers: {
          'X-Admin-Key': adminUser.adminKey,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.success) {
        return true;
      } else {
        setError(data.error || 'Erro ao revogar token');
        return false;
      }
    } catch (err) {
      setError('Erro ao revogar token');
      return false;
    } finally {
      setLoading(false);
    }
  }, [adminUser, API_BASE]);

  /**
   * Verificar estrutura do banco
   */
  const ensureDatabaseStructure = useCallback(async (): Promise<boolean> => {
    if (!adminUser.isAuthenticated) {
      return false;
    }

    try {
      const response = await fetch(`${API_BASE}/admin/ensure-db-structure`, {
        method: 'POST',
        headers: {
          'X-Admin-Key': adminUser.adminKey,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      return data.success;
    } catch (err) {
      console.error('Erro ao verificar estrutura do banco:', err);
      return false;
    }
  }, [adminUser, API_BASE]);

  return {
    // Estado
    isAuthenticated: adminUser.isAuthenticated,
    loading,
    error,

    // Ações
    login,
    logout,
    
    // Gestão de tokens
    createToken,
    listTokens,
    revokeToken,
    
    // Utilitários
    ensureDatabaseStructure,
    
    // Reset error
    clearError: () => setError(null)
  };
}