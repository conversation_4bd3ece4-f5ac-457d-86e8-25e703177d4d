import { PostgreSQLQueryService } from '../services/PostgreSQLQueryService';
import { config } from 'dotenv';

config();

console.log('🔍 TESTANDO TRANSPARÊNCIA - ESTRUTURA CORRIGIDA');
console.log('==============================================\n');

async function testarTransparenciaCorrigida() {
  const pgService = new PostgreSQLQueryService();
  
  try {
    // 1. Testar buscarProtocolosComDadosCompletos
    console.log('1️⃣ Testando buscarProtocolosComDadosCompletos...');
    const protocolosCompletos = await pgService.buscarProtocolosComDadosCompletos(null, 5);
    console.log(`✅ ${protocolosCompletos.length} protocolos encontrados com dados completos`);
    
    if (protocolosCompletos.length > 0) {
      const primeiro = protocolosCompletos[0];
      console.log('\nExemplo de protocolo com dados completos:');
      console.log(`- Protocolo: ${primeiro.id_protocolo}`);
      console.log(`- Interessado: ${primeiro.nome_razao_social || 'Não informado'}`);
      console.log(`- CPF/CNPJ: ${primeiro.cpf_cnpj || 'Não informado'}`);
      console.log(`- Endereço: ${primeiro.endereco || 'Não informado'}`);
      console.log(`- Telefone: ${primeiro.telefone || 'Não informado'}`);
      console.log(`- Email: ${primeiro.email || 'Não informado'}`);
      console.log(`- Assunto: ${primeiro.assunto_descricao || 'Não informado'}`);
      console.log(`- Situação: ${primeiro.situacao_descricao || 'Não informado'}`);
    }
    
    // 2. Testar buscarAlvarasComSocios
    console.log('\n2️⃣ Testando buscarAlvarasComSocios...');
    const alvaras = await pgService.buscarAlvarasComSocios(5);
    console.log(`✅ ${alvaras.length} alvarás encontrados`);
    
    if (alvaras.length > 0) {
      const primeiro = alvaras[0];
      console.log('\nExemplo de alvará:');
      console.log(`- Protocolo: ${primeiro.id_protocolo}`);
      console.log(`- Empresa: ${primeiro.empresa || 'Não informado'}`);
      console.log(`- Documento: ${primeiro.documento || 'Não informado'}`);
      console.log(`- Tipo Pessoa: ${primeiro.tipo_pessoa || 'Não informado'}`);
      console.log(`- Endereço: ${primeiro.endereco_completo || 'Não informado'}`);
      console.log(`- Tipo Alvará: ${primeiro.tipo_alvara || 'Não informado'}`);
      if (primeiro.observacao_socios) {
        console.log(`- ${primeiro.observacao_socios}`);
      }
    }
    
    // 3. Testar buscarCidadaoPorCPF com CPF exemplo
    console.log('\n3️⃣ Testando buscarCidadaoPorCPF...');
    
    // Primeiro, vamos pegar um CPF real do banco para testar
    if (protocolosCompletos.length > 0 && protocolosCompletos[0].cpf_cnpj) {
      const cpfTeste = protocolosCompletos[0].cpf_cnpj;
      console.log(`Testando com CPF/CNPJ: ${cpfTeste}`);
      
      const dadosCidadao = await pgService.buscarCidadaoPorCPF(cpfTeste);
      if (dadosCidadao) {
        console.log('✅ Cidadão encontrado!');
        console.log(`- Nome: ${dadosCidadao.dadosPessoais.nome_razao_social}`);
        console.log(`- CPF/CNPJ: ${dadosCidadao.dadosPessoais.cpf_cnpj}`);
        console.log(`- Total de protocolos: ${dadosCidadao.historico.length}`);
        
        if (dadosCidadao.historico.length > 0) {
          console.log('\nÚltimos protocolos:');
          dadosCidadao.historico.slice(0, 3).forEach((p: any) => {
            console.log(`  - ${p.id_protocolo}: ${p.assunto} (${p.situacao})`);
          });
        }
      } else {
        console.log('ℹ️ Cidadão não encontrado');
      }
    } else {
      console.log('⚠️ Nenhum CPF/CNPJ disponível para teste');
    }
    
    // 4. Verificar dados sensíveis
    console.log('\n4️⃣ Verificando presença de dados sensíveis...');
    let totalComCPF = 0;
    let totalComEmail = 0;
    let totalComTelefone = 0;
    
    protocolosCompletos.forEach(p => {
      if (p.cpf_cnpj) totalComCPF++;
      if (p.email) totalComEmail++;
      if (p.telefone || p.celular) totalComTelefone++;
    });
    
    console.log(`- Protocolos com CPF/CNPJ: ${totalComCPF}/${protocolosCompletos.length}`);
    console.log(`- Protocolos com Email: ${totalComEmail}/${protocolosCompletos.length}`);
    console.log(`- Protocolos com Telefone: ${totalComTelefone}/${protocolosCompletos.length}`);
    
  } catch (error: any) {
    console.error('❌ Erro:', error.message);
  } finally {
    await pgService.fecharConexoes();
  }
  
  console.log('\n✅ TESTE CONCLUÍDO!');
}

// Executar teste
testarTransparenciaCorrigida().catch(console.error);