import { Request, Response, NextFunction } from 'express';
import { cacheService, MessageContext, CacheResponse } from '../services/cacheService';
import { urgencyClassifier } from '../services/urgencyClassifier';
import { rateLimitService } from '../services/rateLimitService';
import { isDiscountTime, getNextDiscountTime } from '../config/redis';

// Extensão do Request para incluir dados de cache
export interface CachedRequest extends Request {
  cacheContext?: MessageContext;
  cacheResult?: CacheResponse;
  conversationHistory?: any[];
  urgencyInfo?: {
    level: 'immediate' | 'normal' | 'batch';
    confidence: number;
    canWait: boolean;
  };
  rateLimitInfo?: import('../services/rateLimitService').RateLimitInfo;
  user?: {
    id: string;
    role: string;
    name?: string;
  };
}

// Interface para resposta padronizada do cache
export interface CacheMiddlewareResponse {
  success: boolean;
  response?: string; // Compatibilidade com teste
  cacheHit?: boolean; // Compatibilidade com teste
  data?: {
    response: string;
    source: 'cache' | 'api' | 'queued';
    cacheType?: 'exact' | 'semantic';
    cost: number;
    timestamp: Date;
    queueInfo?: {
      messageId: string;
      estimatedProcessTime: Date;
      estimatedSavings: number;
    };
  };
  error?: string;
  metadata: {
    urgency: string;
    discountActive: boolean;
    nextDiscountTime: Date;
    processingTime: number;
    tokens?: any;
    model?: string;
    rateLimit?: any;
  };
}

class CacheMiddleware {
  // Middleware principal para interceptar mensagens de chat
  static async interceptChatMessage(
    req: CachedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    const startTime = Date.now();

    try {
      const { message, userId, secretaria, conversationId, forceImmediate } = req.body;

      // Usar userId do usuário autenticado se disponível, senão usar do body
      const effectiveUserId = req.user?.id || userId || 'anonymous';

      // Validar dados obrigatórios
      if (!message || !secretaria) {
        res.status(400).json({
          success: false,
          error: 'Campos obrigatórios: message, secretaria',
          metadata: {
            urgency: 'unknown',
            discountActive: isDiscountTime(),
            nextDiscountTime: getNextDiscountTime(),
            processingTime: Date.now() - startTime,
          }
        });
        return;
      }

      // Criar contexto da mensagem
      const context: MessageContext = {
        userId: effectiveUserId,
        secretaria,
        conversationId,
        urgency: 'normal', // Será atualizado pela classificação
      };

      // PASSO 1: Tentar cache exato
      const exactCache = await cacheService.getExactCache(message, context);
      if (exactCache) {
        const response: CacheMiddlewareResponse = {
          success: true,
          response: exactCache.response, // Compatibilidade com teste
          cacheHit: true, // Compatibilidade com teste
          data: {
            response: exactCache.response,
            source: 'cache',
            cacheType: 'exact',
            timestamp: exactCache.timestamp,
          },
          metadata: {
            urgency: 'cached',
            processingTime: Date.now() - startTime,
            // Informações sensíveis removidas para usuários finais
          }
        };

        res.json(response);
        return;
      }

      // PASSO 2: Tentar cache semântico
      const semanticCache = await cacheService.getSemanticCache(message, context);
      if (semanticCache) {
        const response: CacheMiddlewareResponse = {
          success: true,
          response: semanticCache.response, // Compatibilidade com teste
          cacheHit: true, // Compatibilidade com teste
          data: {
            response: semanticCache.response,
            source: 'cache',
            cacheType: 'semantic',
            timestamp: semanticCache.timestamp,
          },
          metadata: {
            urgency: 'cached_similar',
            processingTime: Date.now() - startTime,
            // Informações sensíveis removidas para usuários finais
          }
        };

        res.json(response);
        return;
      }

      // PASSO 3: Classificar urgência
      const urgencyResult = urgencyClassifier.classify(message);
      context.urgency = urgencyResult.level;

      // Adicionar informações de urgência ao request
      req.urgencyInfo = {
        level: urgencyResult.level,
        confidence: urgencyResult.confidence,
        canWait: urgencyClassifier.canWaitForDiscount(message),
      };

      // PASSO 4: Verificar rate limiting
      const userRole = req.user?.role || 'operador'; // Assumir operador se não especificado
      const rateLimitResult = await rateLimitService.checkRateLimit(userId, userRole, 500);
      
      if (!rateLimitResult.allowed) {
        res.status(429).json({
          success: false,
          error: rateLimitResult.error,
          metadata: {
            urgency: urgencyResult.level,
            processingTime: Date.now() - startTime,
            // Informações sensíveis removidas para usuários finais
          }
        });
        return;
      }

      // PASSO 5: Processar imediatamente (sem filas forçadas)
      // Aplicar desconto automaticamente se estiver no horário
      req.cacheContext = context;
      req.rateLimitInfo = rateLimitResult.info;
      next();

    } catch (error) {
      console.error('Erro no cache middleware:', error);
      
      const response: CacheMiddlewareResponse = {
        success: false,
        error: 'Erro interno no sistema de cache',
        metadata: {
          urgency: 'error',
          processingTime: Date.now() - startTime,
          // Informações sensíveis removidas para usuários finais
        }
      };

      res.status(500).json(response);
    }
  }

  // Middleware para salvar resposta no cache após processamento
  static async saveCacheResponse(
    req: CachedRequest, 
    res: Response, 
    next: NextFunction
  ): Promise<void> {
    // Interceptar a resposta original
    const originalSend = res.json;
    
    res.json = function(body: any) {
      // Salvar no cache se foi uma resposta bem-sucedida da IA
      if (body.success && body.data && req.cacheContext && req.body.message) {
        const cost = body.data.cost || 0.00219; // Custo padrão
        
        cacheService.setExactCache(
          req.body.message,
          req.cacheContext,
          body.data.response,
          cost
        ).catch(error => {
          console.error('Erro ao salvar cache:', error);
        });
      }
      
      // Chamar método original
      return originalSend.call(this, body);
    };
    
    next();
  }

  // Middleware para registrar uso após processamento bem-sucedido
  static async recordUsage(
    req: CachedRequest, 
    res: Response, 
    next: NextFunction
  ): Promise<void> {
    // Interceptar resposta para registrar uso
    const originalSend = res.json;
    
    res.json = function(body: any) {
      // Registrar uso se foi processamento bem-sucedido
      if (body.success && body.data && req.rateLimitInfo) {
        const tokensUsed = body.data.tokens?.total || 500; // Estimativa se não disponível
        
        rateLimitService.recordUsage(
          req.rateLimitInfo.userId,
          tokensUsed
        ).catch(error => {
          console.error('Erro ao registrar uso:', error);
        });
      }
      
      return originalSend.call(this, body);
    };
    
    next();
  }

  // Middleware para métricas
  static async trackMetrics(
    req: CachedRequest, 
    res: Response, 
    next: NextFunction
  ): Promise<void> {
    const startTime = Date.now();
    
    // Interceptar resposta para coletar métricas
    const originalSend = res.json;
    
    res.json = function(body: any) {
      const processingTime = Date.now() - startTime;
      const isCache = body.data?.source === 'cache';
      const cost = body.data?.cost || 0;
      
      // Salvar métricas assíncronamente
      setImmediate(() => {
        CacheMiddleware.saveMetrics({
          userId: req.body.userId,
          secretaria: req.body.secretaria,
          isCache,
          cost,
          processingTime,
          urgency: req.urgencyInfo?.level || 'unknown',
        });
      });
      
      return originalSend.call(this, body);
    };
    
    next();
  }

  // Salvar métricas no Redis
  private static async saveMetrics(metrics: {
    userId: string;
    secretaria: string;
    isCache: boolean;
    cost: number;
    processingTime: number;
    urgency: string;
  }): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Incrementar contadores
      await Promise.all([
        // Métricas gerais
        cacheService['incrementMetric']('total_requests'),
        cacheService['incrementMetric'](metrics.isCache ? 'cache_hits' : 'cache_misses'),
        
        // Métricas por secretaria
        cacheService['incrementMetric'](`requests_${metrics.secretaria}`),
        
        // Métricas de custo
        // Implementar acumulação de custos
      ]);
      
    } catch (error) {
      console.error('Erro ao salvar métricas:', error);
    }
  }
}

// Middleware combinado para chat
export const chatCacheMiddleware = [
  CacheMiddleware.trackMetrics,
  CacheMiddleware.interceptChatMessage,
  CacheMiddleware.recordUsage,
  CacheMiddleware.saveCacheResponse,
];

// Middleware individual para outras rotas
export const cacheMiddleware = CacheMiddleware.interceptChatMessage;
export const saveCache = CacheMiddleware.saveCacheResponse;
export const trackMetrics = CacheMiddleware.trackMetrics;

export default CacheMiddleware;