import crypto from 'crypto';
import { redisCache, cacheTTL, cacheKeys, discountConfig, isDiscountTime } from '../config/redis';

// Tipos para o sistema de cache
export interface CacheResponse {
  response: string;
  timestamp: Date;
  cost: number;
  source: 'cache' | 'api';
  cacheType?: 'exact' | 'semantic' | 'context';
  tokens?: {
    prompt: number;
    completion: number;
    total: number;
  };
}

export interface MessageContext {
  userId: string;
  secretaria: string;
  conversationId?: string;
  urgency: 'immediate' | 'normal' | 'batch';
}

export interface CacheMetrics {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  totalSavings: number;
  hitRate: number;
}

class CacheService {
  // Gerar hash MD5 para cache exato
  private generateCacheKey(message: string, context: MessageContext): string {
    const cacheData = {
      message: message.toLowerCase().trim(),
      secretaria: context.secretaria,
    };
    
    const hash = crypto.createHash('md5')
                      .update(JSON.stringify(cacheData))
                      .digest('hex');
    
    return cacheKeys.EXACT(hash);
  }

  // Verificar cache de resposta exata
  async getExactCache(message: string, context: MessageContext): Promise<CacheResponse | null> {
    try {
      const key = this.generateCacheKey(message, context);
      const cached = await redisCache.get(key);
      
      if (cached) {
        const response = JSON.parse(cached);
        await this.incrementMetric('cache_hits');
        
        return {
          ...response,
          source: 'cache' as const,
          cacheType: 'exact' as const,
        };
      }
      
      return null;
    } catch (error) {
      console.error('Erro ao buscar cache exato:', error);
      return null;
    }
  }

  // Salvar resposta no cache exato
  async setExactCache(
    message: string, 
    context: MessageContext, 
    response: string, 
    cost: number,
    tokens?: { prompt: number; completion: number; total: number }
  ): Promise<void> {
    try {
      const key = this.generateCacheKey(message, context);
      const cacheData: CacheResponse = {
        response,
        timestamp: new Date(),
        cost,
        source: 'api',
        cacheType: 'exact',
        tokens
      };
      
      await redisCache.setex(key, cacheTTL.EXACT_MATCH, JSON.stringify(cacheData));
      
      // Também salvar no índice semântico para busca futura
      await this.addToSemanticIndex(message, response, context);
      
      // Salvar economia acumulada
      await this.addSavings(cost);
      
    } catch (error) {
      console.error('Erro ao salvar cache exato:', error);
    }
  }

  // Buscar respostas similares semanticamente
  async getSemanticCache(message: string, context: MessageContext): Promise<CacheResponse | null> {
    try {
      // Buscar no índice semântico da secretaria
      const semanticKey = cacheKeys.SEMANTIC(context.secretaria, '*');
      const keys = await redisCache.keys(semanticKey);
      
      for (const key of keys) {
        const cached = await redisCache.get(key);
        if (cached) {
          const data = JSON.parse(cached);
          
          // Calcular similaridade simples (por palavras-chave)
          const similarity = this.calculateSimilarity(message, data.originalMessage);
          
          if (similarity > 0.95) { // 95% de similaridade
            await this.incrementMetric('cache_hits');
            
            return {
              response: data.response,
              timestamp: new Date(data.timestamp),
              cost: 0, // Cache hit não tem custo
              source: 'cache' as const,
              cacheType: 'semantic' as const,
            };
          }
        }
      }
      
      return null;
    } catch (error) {
      console.error('Erro ao buscar cache semântico:', error);
      return null;
    }
  }

  // Adicionar ao índice semântico
  private async addToSemanticIndex(
    message: string, 
    response: string, 
    context: MessageContext
  ): Promise<void> {
    try {
      // Extrair tópico principal da mensagem
      const topic = this.extractTopic(message);
      const key = cacheKeys.SEMANTIC(context.secretaria, topic);
      
      const semanticData = {
        originalMessage: message,
        response,
        timestamp: new Date(),
        usageCount: 1,
      };
      
      await redisCache.setex(key, cacheTTL.SEMANTIC_MATCH, JSON.stringify(semanticData));
    } catch (error) {
      console.error('Erro ao adicionar ao índice semântico:', error);
    }
  }

  // Calcular similaridade simples entre mensagens
  private calculateSimilarity(message1: string, message2: string): number {
    const normalize = (text: string) => 
      text.toLowerCase()
          .replace(/[^\w\s]/g, '')
          .split(/\s+/)
          .filter(word => word.length > 2);
    
    const words1 = normalize(message1);
    const words2 = normalize(message2);
    
    const intersection = words1.filter(word => words2.includes(word));
    const union = [...new Set([...words1, ...words2])];
    
    return intersection.length / union.length;
  }

  // Extrair tópico principal da mensagem
  private extractTopic(message: string): string {
    const keywords = {
      funcionarios: ['funcionário', 'servidor', 'pessoal', 'equipe', 'colaborador'],
      orcamento: ['orçamento', 'verba', 'recurso', 'dinheiro', 'gasto'],
      relatorio: ['relatório', 'dados', 'informação', 'estatística'],
      processo: ['processo', 'protocolo', 'tramitação', 'andamento'],
      servico: ['serviço', 'atendimento', 'procedimento', 'ação'],
    };
    
    const messageLower = message.toLowerCase();
    
    for (const [topic, words] of Object.entries(keywords)) {
      if (words.some(word => messageLower.includes(word))) {
        return topic;
      }
    }
    
    return 'geral';
  }

  // Calcular custo da operação
  calculateCost(inputTokens: number, outputTokens: number, cacheHit: boolean = false): number {
    const isDiscount = isDiscountTime();
    const costs = discountConfig.COSTS;
    
    let inputCost: number;
    let outputCost: number;
    
    if (isDiscount) {
      inputCost = cacheHit ? costs.INPUT_CACHE_HIT_DISCOUNT : costs.INPUT_CACHE_MISS_DISCOUNT;
      outputCost = costs.OUTPUT_DISCOUNT;
    } else {
      inputCost = cacheHit ? costs.INPUT_CACHE_HIT : costs.INPUT_CACHE_MISS;
      outputCost = costs.OUTPUT;
    }
    
    return (inputTokens * inputCost) + (outputTokens * outputCost);
  }

  // Incrementar métricas
  private async incrementMetric(metric: string): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const key = cacheKeys.METRICS(metric, today);
      await redisCache.incr(key);
      await redisCache.expire(key, 86400 * 7); // Manter por 7 dias
    } catch (error) {
      console.error('Erro ao incrementar métrica:', error);
    }
  }

  // Obter métricas do cache
  async getCacheMetrics(): Promise<CacheMetrics> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const totalRequests = parseInt(await redisCache.get(cacheKeys.METRICS('total_requests', today)) || '0');
      const cacheHits = parseInt(await redisCache.get(cacheKeys.METRICS('cache_hits', today)) || '0');
      const cacheMisses = totalRequests - cacheHits;
      const totalSavings = parseFloat(await redisCache.get(cacheKeys.METRICS('total_savings', today)) || '0');
      
      return {
        totalRequests,
        cacheHits,
        cacheMisses,
        totalSavings,
        hitRate: totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0,
      };
    } catch (error) {
      console.error('Erro ao obter métricas:', error);
      return {
        totalRequests: 0,
        cacheHits: 0,
        cacheMisses: 0,
        totalSavings: 0,
        hitRate: 0,
      };
    }
  }

  // Limpar cache expirado
  async cleanExpiredCache(): Promise<void> {
    try {
      // Esta operação é feita automaticamente pelo Redis TTL
      console.log('Cache cleanup executado');
    } catch (error) {
      console.error('Erro na limpeza do cache:', error);
    }
  }

  // Verificar status do Redis
  async healthCheck(): Promise<boolean> {
    try {
      await redisCache.ping();
      return true;
    } catch (error) {
      console.error('Redis health check falhou:', error);
      return false;
    }
  }

  // Adicionar economia ao total
  private async addSavings(amount: number): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const key = cacheKeys.METRICS('total_savings', today);
      await redisCache.incrbyfloat(key, amount);
      await redisCache.expire(key, 86400 * 7); // Manter por 7 dias
    } catch (error) {
      console.error('Erro ao adicionar economia:', error);
    }
  }

  // Registrar requisição
  async recordRequest(): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const key = cacheKeys.METRICS('total_requests', today);
      await redisCache.incr(key);
      await redisCache.expire(key, 86400 * 7); // Manter por 7 dias
    } catch (error) {
      console.error('Erro ao registrar requisição:', error);
    }
  }
}

export const cacheService = new CacheService();
export default cacheService;