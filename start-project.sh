#!/bin/bash

echo "🔍 === VERIFICANDO REDIS ==="

# Função para testar conexão Redis
test_redis() {
    timeout 3 bash -c "</dev/tcp/localhost/6379" 2>/dev/null
    return $?
}

# Testar conexão
if test_redis; then
    echo "✅ Redis está rodando na porta 6379"
else
    echo "❌ Redis não está acessível na porta 6379"
    echo ""
    echo "🔧 SOLUÇÕES:"
    echo "1. Verificar se redis-server.exe está rodando no Windows"
    echo "2. Executar: netstat -an | findstr 6379"
    echo "3. Se não estiver, executar: redis-server.exe"
    echo ""
    exit 1
fi

echo ""
echo "🚀 === INICIANDO PROJETO ==="
echo "Backend: http://localhost:3001"
echo "Frontend: http://localhost:3000"
echo ""

# Iniciar projeto
npm run dev