import { motion } from 'framer-motion';
import { IconType } from 'react-icons';

interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: IconType;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  delay?: number;
}

export function StatsCard({ 
  title, 
  value, 
  subtitle, 
  icon: Icon, 
  color, 
  trend,
  delay = 0 
}: StatsCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay, duration: 0.5 }}
      whileHover={{ y: -5, scale: 1.02 }}
      className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-6 group hover:bg-white/10 transition-all duration-300"
    >
      {/* Header com ícone */}
      <div className="flex items-center justify-between mb-4">
        <div className={`w-12 h-12 bg-gradient-to-r ${color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform`}>
          <Icon className="text-white text-xl" />
        </div>
        
        {trend && (
          <div className={`text-sm font-medium px-2 py-1 rounded-full ${
            trend.isPositive 
              ? 'text-green-400 bg-green-400/20' 
              : 'text-red-400 bg-red-400/20'
          }`}>
            {trend.isPositive ? '+' : ''}{trend.value}%
          </div>
        )}
      </div>

      {/* Conteúdo principal */}
      <div className="space-y-1">
        <p className="text-gray-400 text-sm font-medium">{title}</p>
        <p className="text-3xl font-bold text-white">
          {typeof value === 'number' ? value.toLocaleString('pt-BR') : value}
        </p>
        {subtitle && (
          <p className="text-gray-500 text-xs">{subtitle}</p>
        )}
      </div>
    </motion.div>
  );
}