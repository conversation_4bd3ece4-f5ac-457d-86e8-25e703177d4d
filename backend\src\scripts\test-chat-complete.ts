import axios from 'axios';
import { config } from 'dotenv';

config();

const API_URL = 'http://localhost:3001';

async function testarChatCompleto() {
  console.log('🔍 TESTANDO CHAT COMPLETO COM FORMATAÇÃO FLUIDA');
  console.log('==============================================\n');
  
  try {
    // 1. Login
    console.log('1️⃣ Fazendo login...');
    const loginResponse = await axios.post(`${API_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123',
      secretaria: 'administracao'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login realizado com sucesso');
    
    // 2. Buscar conversa ativa
    console.log('\n2️⃣ Buscando conversa ativa...');
    const conversationResponse = await axios.get(`${API_URL}/api/conversations/active`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const conversationId = conversationResponse.data.data.id;
    console.log(`✅ Conversa ativa: ${conversationId}`);
    
    // 3. Testar mensagem com formatação
    console.log('\n3️⃣ Enviando mensagem de teste...');
    const pergunta = "Qual o processo mais atual que temos no sistema?";
    console.log(`Pergunta: "${pergunta}"`);
    
    const chatResponse = await axios.post(
      `${API_URL}/api/chat/message`,
      {
        message: pergunta,
        secretaria: 'administracao'
      },
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    
    console.log('\n✅ Resposta recebida:');
    console.log('-------------------');
    console.log(chatResponse.data.response);
    console.log('-------------------');
    
    // Analisar formatação
    const resposta = chatResponse.data.response;
    const temAsteriscos = (resposta.match(/\*/g) || []).length;
    const temBullets = (resposta.match(/•/g) || []).length;
    const temHashtags = (resposta.match(/#/g) || []).length;
    
    console.log('\n📊 Análise da formatação:');
    console.log(`- Asteriscos encontrados: ${temAsteriscos}`);
    console.log(`- Bullets encontrados: ${temBullets}`);
    console.log(`- Hashtags encontradas: ${temHashtags}`);
    
    if (temAsteriscos > 4 || temBullets > 0) {
      console.log('⚠️ ATENÇÃO: Ainda há formatação excessiva na resposta!');
    } else {
      console.log('✅ Formatação está mais fluida e natural!');
    }
    
    // 4. Verificar se a conversa foi salva
    console.log('\n4️⃣ Verificando histórico de conversas...');
    const conversationsResponse = await axios.get(`${API_URL}/api/conversations`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const totalConversas = conversationsResponse.data.data.length;
    console.log(`✅ Total de conversas no histórico: ${totalConversas}`);
    
    if (totalConversas === 0) {
      console.log('⚠️ PROBLEMA: Nenhuma conversa no histórico!');
    } else {
      console.log('✅ Conversas estão sendo salvas corretamente!');
      
      // Mostrar última conversa
      const ultimaConversa = conversationsResponse.data.data[0];
      console.log(`\nÚltima conversa:`);
      console.log(`- ID: ${ultimaConversa.id}`);
      console.log(`- Título: ${ultimaConversa.title}`);
      console.log(`- Mensagens: ${ultimaConversa.message_count || 0}`);
      console.log(`- Última mensagem: ${ultimaConversa.last_message || 'N/A'}`);
    }
    
    // 5. Testar pergunta sobre CPF
    console.log('\n5️⃣ Testando transparência total...');
    const perguntaCPF = "Qual o CPF do requerente do protocolo 20250060121?";
    console.log(`Pergunta: "${perguntaCPF}"`);
    
    const cpfResponse = await axios.post(
      `${API_URL}/api/chat/message`,
      {
        message: perguntaCPF,
        secretaria: 'administracao'
      },
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    
    console.log('\n✅ Resposta sobre CPF:');
    console.log('-------------------');
    console.log(cpfResponse.data.response);
    console.log('-------------------');
    
    // Verificar se incluiu CPF
    const temCPF = cpfResponse.data.response.match(/\d{3}\.\d{3}\.\d{3}-\d{2}/);
    console.log(`\n✅ Resposta contém CPF? ${temCPF ? 'SIM' : 'NÃO'}`);
    
  } catch (error: any) {
    console.error('\n❌ Erro:', error.response?.data || error.message);
  }
  
  console.log('\n✅ TESTE CONCLUÍDO!');
}

// Executar teste
testarChatCompleto().catch(console.error);