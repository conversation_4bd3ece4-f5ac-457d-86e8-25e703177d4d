'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { FaTrash, FaEdit, FaCheck, FaTimes } from 'react-icons/fa';
import { Conversation } from '@/hooks/useConversations';

interface ConversationItemProps {
  conversation: Conversation;
  isActive: boolean;
  onClick: () => void;
  onDelete: (id: string) => void;
  onUpdateTitle: (id: string, title: string) => void;
}

export function ConversationItem({
  conversation,
  isActive,
  onClick,
  onDelete,
  onUpdateTitle
}: ConversationItemProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(conversation.title);
  const [isHovered, setIsHovered] = useState(false);

  const handleSaveTitle = () => {
    if (editTitle.trim() && editTitle !== conversation.title) {
      onUpdateTitle(conversation.id, editTitle.trim());
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditTitle(conversation.title);
    setIsEditing(false);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('Tem certeza que deseja deletar esta conversa?')) {
      onDelete(conversation.id);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      return 'Hoje';
    } else if (days === 1) {
      return 'Ontem';
    } else if (days < 7) {
      return `${days} dias`;
    } else {
      return date.toLocaleDateString('pt-BR', { 
        day: '2-digit', 
        month: '2-digit' 
      });
    }
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      whileHover={{ scale: 1.02 }}
      className={`
        relative p-3 rounded-lg cursor-pointer transition-all duration-200
        ${isActive
          ? 'bg-blue-100 dark:bg-blue-600/20 border border-blue-300 dark:border-blue-500/50 shadow-lg'
          : 'bg-gray-100 dark:bg-white/5 hover:bg-gray-200 dark:hover:bg-white/10 border border-transparent'
        }
      `}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-start justify-between gap-2">
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <div className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
              <input
                type="text"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleSaveTitle();
                  if (e.key === 'Escape') handleCancelEdit();
                }}
                className="flex-1 px-2 py-1 text-sm bg-gray-100 dark:bg-white/10 border border-gray-300 dark:border-white/20 rounded text-gray-900 dark:text-white focus:outline-none focus:border-blue-500"
                autoFocus
              />
              <button
                onClick={handleSaveTitle}
                className="p-1 text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300"
              >
                <FaCheck className="text-xs" />
              </button>
              <button
                onClick={handleCancelEdit}
                className="p-1 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
              >
                <FaTimes className="text-xs" />
              </button>
            </div>
          ) : (
            <>
              <h3 className={`
                text-sm font-medium truncate
                ${isActive ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-200'}
              `}>
                {conversation.title}
              </h3>
              {conversation.last_message && (
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                  {conversation.last_message}
                </p>
              )}
            </>
          )}
        </div>

        {/* Action buttons - only show on hover or when active */}
        {(isHovered || isActive) && !isEditing && (
          <div className="flex items-center gap-1 transition-opacity">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsEditing(true);
              }}
              className="p-1 text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              title="Editar título"
            >
              <FaEdit className="text-xs" />
            </button>
            <button
              onClick={handleDelete}
              className="p-1 text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
              title="Deletar conversa"
            >
              <FaTrash className="text-xs" />
            </button>
          </div>
        )}
      </div>

      {/* Metadata */}
      <div className="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-500">
        <span>{formatDate(conversation.created_at)}</span>
        <span>{conversation.message_count} msgs</span>
      </div>

      {/* Active indicator */}
      {isActive && (
        <motion.div
          layoutId="activeIndicator"
          className="absolute left-0 top-0 bottom-0 w-1 bg-blue-600 dark:bg-blue-500 rounded-r-full"
        />
      )}
    </motion.div>
  );
}