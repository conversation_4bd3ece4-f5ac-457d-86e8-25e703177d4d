import { redisCache, cacheKeys } from '../config/redis';
import { EventEmitter } from 'events';

// Tipos para monitoramento
interface CostEntry {
  timestamp: Date;
  userId: string;
  secretaria: string;
  cost: number;
  tokens: {
    prompt: number;
    completion: number;
    total: number;
  };
  cacheHit: boolean;
  discountApplied: boolean;
}

interface CostAlert {
  type: 'daily' | 'monthly' | 'emergency';
  threshold: number;
  current: number;
  percentage: number;
  message: string;
}

interface CostReport {
  period: string;
  totalCost: number;
  totalSavings: number;
  messagesProcessed: number;
  cacheHitRate: number;
  averageCostPerMessage: number;
  bySecretaria: Record<string, number>;
  byUser: Record<string, number>;
  alerts: CostAlert[];
}

class CostMonitoringService extends EventEmitter {
  private dailyBudget: number;
  private monthlyBudget: number;
  private emergencyThreshold: number;

  constructor() {
    super();
    this.dailyBudget = parseFloat(process.env.MAX_DAILY_BUDGET || '50.00');
    this.monthlyBudget = parseFloat(process.env.MAX_MONTHLY_BUDGET || '1000.00');
    this.emergencyThreshold = parseFloat(process.env.EMERGENCY_STOP_BUDGET || '100.00');
  }

  // Registrar novo custo
  async recordCost(entry: CostEntry): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const month = today.substring(0, 7);

      // Salvar entrada detalhada
      const entryKey = `cost_entry:${today}:${Date.now()}`;
      await redisCache.setex(entryKey, 86400 * 30, JSON.stringify(entry)); // 30 dias

      // Atualizar totais diários
      const dailyKey = cacheKeys.METRICS('daily_cost', today);
      await redisCache.incrbyfloat(dailyKey, entry.cost);
      await redisCache.expire(dailyKey, 86400 * 7);

      // Atualizar totais mensais
      const monthlyKey = cacheKeys.METRICS('monthly_cost', month);
      await redisCache.incrbyfloat(monthlyKey, entry.cost);
      await redisCache.expire(monthlyKey, 86400 * 90);

      // Atualizar por secretaria
      const secretariaKey = cacheKeys.METRICS(`cost_by_secretaria:${entry.secretaria}`, today);
      await redisCache.incrbyfloat(secretariaKey, entry.cost);
      await redisCache.expire(secretariaKey, 86400 * 30);

      // Verificar alertas
      await this.checkAlerts(today, month);

      // Log de custo
      console.log(`💰 Custo registrado: $${entry.cost.toFixed(4)} | Tokens: ${entry.tokens.total} | Cache: ${entry.cacheHit ? 'HIT' : 'MISS'}`);

    } catch (error) {
      console.error('Erro ao registrar custo:', error);
    }
  }

  // Verificar alertas de custo
  private async checkAlerts(today: string, month: string): Promise<void> {
    try {
      // Custo diário
      const dailyCost = parseFloat(await redisCache.get(cacheKeys.METRICS('daily_cost', today)) || '0');
      const dailyPercentage = (dailyCost / this.dailyBudget) * 100;

      // Custo mensal
      const monthlyCost = parseFloat(await redisCache.get(cacheKeys.METRICS('monthly_cost', month)) || '0');
      const monthlyPercentage = (monthlyCost / this.monthlyBudget) * 100;

      // Verificar limite de emergência
      if (dailyCost >= this.emergencyThreshold) {
        const alert: CostAlert = {
          type: 'emergency',
          threshold: this.emergencyThreshold,
          current: dailyCost,
          percentage: (dailyCost / this.emergencyThreshold) * 100,
          message: `ALERTA CRÍTICO: Custo diário ($${dailyCost.toFixed(2)}) excedeu limite de emergência!`
        };
        
        this.emit('emergency_alert', alert);
        console.error(`🚨 ${alert.message}`);
        
        // Salvar alerta no Redis
        await this.saveAlert(alert);
      }

      // Alertas diários
      if (dailyPercentage >= 80) {
        const alert: CostAlert = {
          type: 'daily',
          threshold: this.dailyBudget,
          current: dailyCost,
          percentage: dailyPercentage,
          message: `Alerta: ${dailyPercentage.toFixed(1)}% do orçamento diário consumido`
        };
        
        this.emit('cost_alert', alert);
        console.warn(`⚠️ ${alert.message}`);
        await this.saveAlert(alert);
      }

      // Alertas mensais
      if (monthlyPercentage >= 70) {
        const alert: CostAlert = {
          type: 'monthly',
          threshold: this.monthlyBudget,
          current: monthlyCost,
          percentage: monthlyPercentage,
          message: `Alerta: ${monthlyPercentage.toFixed(1)}% do orçamento mensal consumido`
        };
        
        this.emit('cost_alert', alert);
        console.warn(`⚠️ ${alert.message}`);
        await this.saveAlert(alert);
      }

    } catch (error) {
      console.error('Erro ao verificar alertas:', error);
    }
  }

  // Salvar alerta no Redis
  private async saveAlert(alert: CostAlert): Promise<void> {
    const alertKey = `cost_alert:${new Date().toISOString()}`;
    await redisCache.setex(alertKey, 86400 * 7, JSON.stringify(alert));
  }

  // Obter relatório de custos
  async getCostReport(period: 'daily' | 'monthly', date?: string): Promise<CostReport> {
    try {
      const targetDate = date || new Date().toISOString().split('T')[0];
      const periodKey = period === 'daily' ? targetDate : targetDate.substring(0, 7);

      // Buscar dados agregados
      const totalCost = parseFloat(
        await redisCache.get(cacheKeys.METRICS(`${period}_cost`, periodKey)) || '0'
      );

      // Buscar economia total
      const totalSavings = parseFloat(
        await redisCache.get(cacheKeys.METRICS('total_savings', periodKey)) || '0'
      );

      // Buscar total de mensagens
      const totalRequests = parseInt(
        await redisCache.get(cacheKeys.METRICS('total_requests', periodKey)) || '0'
      );

      // Buscar cache hits
      const cacheHits = parseInt(
        await redisCache.get(cacheKeys.METRICS('cache_hits', periodKey)) || '0'
      );

      // Buscar custos por secretaria
      const secretarias = ['administracao', 'financas', 'saude', 'educacao', 'obras', 'assistencia_social', 'meio_ambiente'];
      const bySecretaria: Record<string, number> = {};
      
      for (const sec of secretarias) {
        const cost = parseFloat(
          await redisCache.get(cacheKeys.METRICS(`cost_by_secretaria:${sec}`, periodKey)) || '0'
        );
        if (cost > 0) bySecretaria[sec] = cost;
      }

      // Buscar alertas ativos
      const alertKeys = await redisCache.keys(`cost_alert:${periodKey}*`);
      const alerts: CostAlert[] = [];
      
      for (const key of alertKeys) {
        const alertData = await redisCache.get(key);
        if (alertData) alerts.push(JSON.parse(alertData));
      }

      return {
        period: periodKey,
        totalCost,
        totalSavings,
        messagesProcessed: totalRequests,
        cacheHitRate: totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0,
        averageCostPerMessage: totalRequests > 0 ? totalCost / totalRequests : 0,
        bySecretaria,
        byUser: {}, // Pode ser implementado se necessário
        alerts
      };

    } catch (error) {
      console.error('Erro ao gerar relatório de custos:', error);
      throw error;
    }
  }

  // Obter status de orçamento em tempo real
  async getBudgetStatus(): Promise<any> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const month = today.substring(0, 7);

      const dailyCost = parseFloat(await redisCache.get(cacheKeys.METRICS('daily_cost', today)) || '0');
      const monthlyCost = parseFloat(await redisCache.get(cacheKeys.METRICS('monthly_cost', month)) || '0');

      return {
        daily: {
          spent: dailyCost,
          budget: this.dailyBudget,
          remaining: this.dailyBudget - dailyCost,
          percentage: (dailyCost / this.dailyBudget) * 100,
          status: dailyCost >= this.dailyBudget ? 'exceeded' : 
                  dailyCost >= this.dailyBudget * 0.8 ? 'warning' : 'ok'
        },
        monthly: {
          spent: monthlyCost,
          budget: this.monthlyBudget,
          remaining: this.monthlyBudget - monthlyCost,
          percentage: (monthlyCost / this.monthlyBudget) * 100,
          status: monthlyCost >= this.monthlyBudget ? 'exceeded' : 
                  monthlyCost >= this.monthlyBudget * 0.7 ? 'warning' : 'ok'
        },
        emergency: {
          threshold: this.emergencyThreshold,
          triggered: dailyCost >= this.emergencyThreshold
        }
      };

    } catch (error) {
      console.error('Erro ao obter status do orçamento:', error);
      throw error;
    }
  }

  // Exportar dados para análise
  async exportCostData(startDate: string, endDate: string): Promise<any[]> {
    try {
      const pattern = `cost_entry:*`;
      const keys = await redisCache.keys(pattern);
      const entries: any[] = [];

      for (const key of keys) {
        const data = await redisCache.get(key);
        if (data) {
          const entry = JSON.parse(data);
          const entryDate = new Date(entry.timestamp).toISOString().split('T')[0];
          
          if (entryDate >= startDate && entryDate <= endDate) {
            entries.push(entry);
          }
        }
      }

      return entries.sort((a, b) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

    } catch (error) {
      console.error('Erro ao exportar dados de custo:', error);
      throw error;
    }
  }
}

export const costMonitoringService = new CostMonitoringService();

// Listeners para alertas críticos
costMonitoringService.on('emergency_alert', (alert: CostAlert) => {
  // Aqui pode implementar notificações por email, SMS, etc.
  console.error(`🚨🚨🚨 ALERTA DE EMERGÊNCIA: ${alert.message}`);
  // TODO: Implementar parada automática do sistema se necessário
});

costMonitoringService.on('cost_alert', (alert: CostAlert) => {
  // Log de alertas normais
  console.warn(`⚠️ Alerta de custo: ${alert.message}`);
});

export default costMonitoringService;