'use client';

import { useState, useEffect } from 'react';
import { Logo } from '@/components/ui';
import { ChatWindow, MessageInput } from '@/components/chat';
import { useChat } from '@/hooks/useChat';
import { useTokenAuth } from '@/hooks/useTokenAuth';

export default function SimpleChatPage() {
  // Autenticação por token
  const { token, isAuthenticated, user, isLoading: authLoading, error: authError } = useTokenAuth();
  
  const { messages, isLoading, sendMessage } = useChat(token || '', user);

  const handleSendMessage = async (message: string) => {
    try {
      await sendMessage(message);
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  // Mostrar loading enquanto autentica
  if (authLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-pv-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pv-blue-primary mx-auto mb-4"></div>
          <p className="text-pv-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  // Mostrar erro se não conseguir autenticar
  if (authError) {
    return (
      <div className="h-screen flex items-center justify-center bg-pv-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-pv-gray-800 mb-2">Acesso Negado</h1>
          <p className="text-pv-gray-600 mb-4">{authError}</p>
          <p className="text-sm text-pv-gray-500">
            Você precisa de um link autorizado para acessar este chatbot.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* Header */}
      <header className="bg-gradient-to-r from-pv-blue-primary to-pv-blue-secondary text-white shadow-lg">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Logo variant="horizontal" size="sm" />
              <div>
                <h1 className="text-xl font-semibold">Chatbot Inteligente</h1>
                <p className="text-pv-blue-100 text-sm">Prefeitura de Valparaíso de Goiás</p>
                {user && (
                  <p className="text-xs text-pv-blue-200">
                    👤 {user.name} • ID: {user.id}
                  </p>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-pv-blue-100">Sistema Online</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="border-b border-pv-gray-200 bg-white px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-pv-gray-800">Chat de Teste</h2>
              <p className="text-sm text-pv-gray-600">
                Teste direto com DeepSeek API
              </p>
            </div>
          </div>
        </div>

        {/* Chat Messages */}
        <ChatWindow 
          messages={messages} 
          isLoading={isLoading} 
          onQuickAction={handleSendMessage}
        />

        {/* Message Input */}
        <MessageInput 
          onSendMessage={handleSendMessage}
          isLoading={isLoading}
          placeholder="Digite sua mensagem de teste..."
        />
      </div>

      {/* Footer */}
      <footer className="border-t border-pv-gray-200 bg-pv-gray-50 px-6 py-3">
        <div className="flex items-center justify-between text-sm text-pv-gray-600">
          <div className="flex items-center space-x-4">
            <span>© 2025 Prefeitura de Valparaíso de Goiás</span>
            <span>•</span>
            <span>Sistema de IA Municipal - TESTE</span>
          </div>
        </div>
      </footer>
    </div>
  );
}