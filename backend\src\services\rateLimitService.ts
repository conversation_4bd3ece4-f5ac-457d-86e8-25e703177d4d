import { redisCache, cacheKeys } from '../config/redis';

// Interface para informações de rate limit
export interface RateLimitInfo {
  userId: string;
  userRole: 'admin' | 'gestor' | 'operador' | 'consulta';
  daily: {
    messagesUsed: number;
    messagesLimit: number;
    tokensUsed: number;
    tokensLimit: number;
    messagesRemaining: number;
    tokensRemaining: number;
  };
  canProceed: boolean;
  resetTime: Date;
}

// Interface para resultado de rate limit check
export interface RateLimitResult {
  allowed: boolean;
  info: RateLimitInfo;
  error?: string;
}

class RateLimitService {
  // Verificar se usuário pode fazer nova requisição
  async checkRateLimit(userId: string, userRole: string, estimatedTokens: number = 500): Promise<RateLimitResult> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Determinar limites baseado na role
      const limits = this.getLimitsForRole(userRole);
      
      // Chaves Redis para contadores
      const messageKey = cacheKeys.USER_LIMIT(userId, `messages:${today}`);
      const tokenKey = cacheKeys.USER_LIMIT(userId, `tokens:${today}`);
      
      // Obter contadores atuais
      const [messagesUsed, tokensUsed] = await Promise.all([
        redisCache.get(messageKey).then(val => parseInt(val || '0')),
        redisCache.get(tokenKey).then(val => parseInt(val || '0'))
      ]);
      
      // Calcular valores restantes
      const messagesRemaining = Math.max(0, limits.messages - messagesUsed);
      const tokensRemaining = Math.max(0, limits.tokens - tokensUsed);
      
      // Verificar se pode proceder
      const canProceedMessages = messagesUsed < limits.messages;
      const canProceedTokens = (tokensUsed + estimatedTokens) <= limits.tokens;
      const canProceed = canProceedMessages && canProceedTokens;
      
      // Calcular horário de reset (meia-noite)
      const resetTime = new Date();
      resetTime.setDate(resetTime.getDate() + 1);
      resetTime.setHours(0, 0, 0, 0);
      
      const info: RateLimitInfo = {
        userId,
        userRole: userRole as any,
        daily: {
          messagesUsed,
          messagesLimit: limits.messages,
          tokensUsed,
          tokensLimit: limits.tokens,
          messagesRemaining,
          tokensRemaining
        },
        canProceed,
        resetTime
      };
      
      // Definir erro se não pode proceder
      let error: string | undefined;
      if (!canProceedMessages) {
        error = `Limite diário de mensagens excedido (${limits.messages}/dia). Reset às 00:00.`;
      } else if (!canProceedTokens) {
        error = `Limite diário de tokens excedido (${limits.tokens}/dia). Reset às 00:00.`;
      }
      
      return {
        allowed: canProceed,
        info,
        error
      };
      
    } catch (error) {
      console.error('Erro ao verificar rate limit:', error);
      
      // Em caso de erro, permitir (fail-open) mas log
      const fallbackInfo: RateLimitInfo = {
        userId,
        userRole: userRole as any,
        daily: {
          messagesUsed: 0,
          messagesLimit: 50,
          tokensUsed: 0,
          tokensLimit: 20000,
          messagesRemaining: 50,
          tokensRemaining: 20000
        },
        canProceed: true,
        resetTime: new Date()
      };
      
      return {
        allowed: true,
        info: fallbackInfo,
        error: 'Erro interno no rate limiting - permitindo requisição'
      };
    }
  }
  
  // Registrar uso após processamento bem-sucedido
  async recordUsage(userId: string, tokensUsed: number): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Chaves Redis para contadores
      const messageKey = cacheKeys.USER_LIMIT(userId, `messages:${today}`);
      const tokenKey = cacheKeys.USER_LIMIT(userId, `tokens:${today}`);
      
      // Incrementar contadores
      await Promise.all([
        redisCache.incr(messageKey),
        redisCache.incrby(tokenKey, tokensUsed)
      ]);
      
      // Definir TTL para expirar à meia-noite
      const now = new Date();
      const midnight = new Date();
      midnight.setDate(midnight.getDate() + 1);
      midnight.setHours(0, 0, 0, 0);
      const ttlSeconds = Math.floor((midnight.getTime() - now.getTime()) / 1000);
      
      await Promise.all([
        redisCache.expire(messageKey, ttlSeconds),
        redisCache.expire(tokenKey, ttlSeconds)
      ]);
      
      console.log(`📊 Rate limit atualizado - User: ${userId}, Messages: +1, Tokens: +${tokensUsed}`);
      
    } catch (error) {
      console.error('Erro ao registrar uso:', error);
      // Não falhar a requisição por erro de rate limit logging
    }
  }
  
  // Obter limites baseado na role do usuário
  private getLimitsForRole(userRole: string): { messages: number; tokens: number } {
    const isAdmin = ['admin', 'gestor'].includes(userRole.toLowerCase());
    
    return {
      messages: isAdmin 
        ? parseInt(process.env.ADMIN_MESSAGES_PER_DAY || '300')
        : parseInt(process.env.USER_MESSAGES_PER_DAY || '50'),
      tokens: isAdmin
        ? parseInt(process.env.ADMIN_TOKENS_PER_DAY || '120000')
        : parseInt(process.env.USER_TOKENS_PER_DAY || '20000')
    };
  }
  
  // Obter estatísticas de uso
  async getUsageStats(userId: string): Promise<RateLimitInfo | null> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Assumir role normal se não especificado (pode ser melhorado)
      const userRole = 'operador'; // TODO: Obter da sessão/contexto
      
      const result = await this.checkRateLimit(userId, userRole, 0);
      return result.info;
      
    } catch (error) {
      console.error('Erro ao obter estatísticas de uso:', error);
      return null;
    }
  }
  
  // Resetar limites de um usuário (apenas para admins)
  async resetUserLimits(userId: string): Promise<boolean> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const messageKey = cacheKeys.USER_LIMIT(userId, `messages:${today}`);
      const tokenKey = cacheKeys.USER_LIMIT(userId, `tokens:${today}`);
      
      await Promise.all([
        redisCache.del(messageKey),
        redisCache.del(tokenKey)
      ]);
      
      console.log(`🔄 Rate limits resetados para usuário: ${userId}`);
      return true;
      
    } catch (error) {
      console.error('Erro ao resetar limites:', error);
      return false;
    }
  }
  
  // Obter relatório de uso geral
  async getUsageReport(): Promise<any> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Buscar todas as chaves de limite de hoje
      const messageKeys = await redisCache.keys(`limit:*:messages:${today}`);
      const tokenKeys = await redisCache.keys(`limit:*:tokens:${today}`);
      
      let totalMessages = 0;
      let totalTokens = 0;
      let activeUsers = 0;
      
      // Somar todos os usos
      for (const key of messageKeys) {
        const usage = await redisCache.get(key);
        if (usage) {
          totalMessages += parseInt(usage);
          activeUsers++;
        }
      }
      
      for (const key of tokenKeys) {
        const usage = await redisCache.get(key);
        if (usage) {
          totalTokens += parseInt(usage);
        }
      }
      
      return {
        date: today,
        totalMessages,
        totalTokens,
        activeUsers,
        averageMessagesPerUser: activeUsers > 0 ? totalMessages / activeUsers : 0,
        averageTokensPerUser: activeUsers > 0 ? totalTokens / activeUsers : 0
      };
      
    } catch (error) {
      console.error('Erro ao gerar relatório de uso:', error);
      return null;
    }
  }
}

export const rateLimitService = new RateLimitService();
export default rateLimitService;