'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui';
import { ChatWindow, MessageInput } from '@/components/chat';
import { FaRobot, FaExpand, FaCompress, FaTimes } from 'react-icons/fa';

interface ChatPanelProps {
  messages: any[];
  isLoading: boolean;
  onSendMessage: (message: string) => Promise<void>;
  onClearChat: () => void;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
  onClose?: () => void;
}

export function ChatPanel({ 
  messages, 
  isLoading, 
  onSendMessage, 
  onClearChat,
  isExpanded = false,
  onToggleExpand,
  onClose
}: ChatPanelProps) {
  const handleSendMessage = async (message: string) => {
    try {
      await onSendMessage(message);
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  return (
    <Card className={`
      ${isExpanded
        ? 'fixed inset-4 z-50 shadow-2xl'
        : 'h-full flex'
      }
      flex-col transition-all duration-300 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700
    `}>
      {/* Header */}
      <CardHeader className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2 text-gray-900 dark:text-white">
            <FaRobot className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <span>Chat Administrativo</span>
            <span className="px-2 py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 text-xs font-medium rounded-full">
              ADMIN
            </span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {/* Expand/Compress Button */}
            {onToggleExpand && (
              <button
                onClick={onToggleExpand}
                className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                title={isExpanded ? "Reduzir" : "Expandir"}
              >
                {isExpanded ? <FaCompress className="w-4 h-4" /> : <FaExpand className="w-4 h-4" />}
              </button>
            )}
            
            {/* Clear Chat Button */}
            <button
              onClick={onClearChat}
              className="p-2 text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="Limpar conversa"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
            
            {/* Close Button */}
            {onClose && (
              <button
                onClick={onClose}
                className="p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors"
                title="Fechar Chat"
              >
                <FaTimes className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        <p className="text-sm text-gray-600 dark:text-gray-300 mt-2">
          Acesso completo ao banco de dados e sistema. Use comandos como "listar protocolos", "estatísticas", "relatórios".
        </p>
      </CardHeader>
      
      {/* Chat Messages */}
      <CardContent className="flex-1 p-0 overflow-hidden">
        <ChatWindow 
          messages={messages} 
          isLoading={isLoading} 
          className="h-full"
        />
      </CardContent>
      
      {/* Message Input */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <MessageInput 
          onSendMessage={handleSendMessage}
          isLoading={isLoading}
          placeholder="Consulte dados do PostgreSQL, gere relatórios ou monitore o sistema..."
        />
      </div>
      
      {/* Status Bar */}
      <div className="border-t border-gray-200 dark:border-gray-700 px-4 py-2 bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-300">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>Conectado ao sistema</span>
          </div>
          <div>
            {messages.length} mensagem{messages.length !== 1 ? 's' : ''}
          </div>
        </div>
      </div>
    </Card>
  );
}
