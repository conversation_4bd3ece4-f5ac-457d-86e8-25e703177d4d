/**
 * Serviço para gerenciar tokens de acesso permanentes
 * Permite criar usuários anônimos com tokens únicos para isolamento de conversas
 */

import crypto from 'crypto';
import { postgresRepository } from '../repositories/PostgreSQLRepository';

interface AccessTokenData {
  token: string;
  userId: number;
  description?: string;
  createdAt: Date;
  isActive: boolean;
}

interface CreateTokenOptions {
  description?: string;
  customToken?: string;
}

export class AccessTokenService {
  /**
   * Gerar token único e seguro
   */
  private generateSecureToken(): string {
    // Gerar token de 32 bytes em base64url (URL-safe)
    return crypto.randomBytes(32)
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * Criar novo token de acesso permanente
   */
  async createAccessToken(options: CreateTokenOptions = {}): Promise<AccessTokenData> {
    try {
      const token = options.customToken || this.generateSecureToken();
      const description = options.description || `Usuário autorizado - ${new Date().toISOString()}`;

      // Verificar se token já existe
      const existingUser = await postgresRepository.findUserByAccessToken(token);
      if (existingUser) {
        throw new Error('Token já existe no sistema');
      }

      // Criar usuário anônimo no PostgreSQL
      const userData = {
        nome: `Usuário Anônimo`,
        cpf: this.generateFakeCPF(), // CPF fake para satisfazer constraint
        email: `anonymous_${token.substring(0, 8)}@valparaiso.go.gov.br`,
        access_token: token,
        description: description,
        conta_ativa: true,
        is_anonymous: true,
        created_at: new Date()
      };

      const user = await postgresRepository.createAnonymousUser(userData);

      return {
        token,
        userId: user.id,
        description,
        createdAt: user.created_at,
        isActive: true
      };

    } catch (error) {
      console.error('Erro ao criar token de acesso:', error);
      throw new Error(`Falha ao criar token de acesso: ${error.message}`);
    }
  }

  /**
   * Validar token de acesso e retornar dados do usuário
   */
  async validateAccessToken(token: string): Promise<any | null> {
    try {
      if (!token || token.length < 10) {
        return null;
      }

      const user = await postgresRepository.findUserByAccessToken(token);
      
      if (!user || !user.conta_ativa) {
        return null;
      }

      return {
        id: user.id,
        name: user.nome,
        email: user.email,
        token: user.access_token,
        isAnonymous: user.is_anonymous || false,
        description: user.description,
        role: 'user', // Role padrão para usuários anônimos
        secretaria: 'geral' // Secretaria padrão
      };

    } catch (error) {
      console.error('Erro ao validar token:', error);
      return null;
    }
  }

  /**
   * Revogar token de acesso (desativar usuário)
   */
  async revokeAccessToken(token: string): Promise<boolean> {
    try {
      const result = await postgresRepository.deactivateUserByToken(token);
      return result;
    } catch (error) {
      console.error('Erro ao revogar token:', error);
      return false;
    }
  }

  /**
   * Listar todos os tokens ativos
   */
  async listActiveTokens(): Promise<AccessTokenData[]> {
    try {
      const users = await postgresRepository.listAnonymousUsers();
      
      return users.map(user => ({
        token: user.access_token,
        userId: user.id,
        description: user.description || 'Usuário anônimo',
        createdAt: user.created_at,
        isActive: user.conta_ativa
      }));

    } catch (error) {
      console.error('Erro ao listar tokens:', error);
      return [];
    }
  }

  /**
   * Gerar link completo com token
   */
  generateAccessLink(token: string, baseUrl?: string): string {
    const base = baseUrl || process.env.FRONTEND_URL || 'http://localhost:3000';
    return `${base}/chat?token=${encodeURIComponent(token)}`;
  }

  /**
   * Gerar CPF fake válido para usuários anônimos
   */
  private generateFakeCPF(): string {
    // Gerar CPF fake baseado em timestamp para garantir unicidade
    const timestamp = Date.now().toString();
    const base = timestamp.substring(timestamp.length - 9);
    
    // Calcular dígitos verificadores
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(base[i]) * (10 - i);
    }
    const digit1 = 11 - (sum % 11);
    const firstDigit = digit1 > 9 ? 0 : digit1;
    
    sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(base[i]) * (11 - i);
    }
    sum += firstDigit * 2;
    const digit2 = 11 - (sum % 11);
    const secondDigit = digit2 > 9 ? 0 : digit2;
    
    return `${base}${firstDigit}${secondDigit}`;
  }

  /**
   * Criar token para usuário do sistema externo
   */
  async createExternalUserToken(options: {
    externalUserId: string;
    name: string;
    email: string;
    secretaria: string;
    cargo?: string;
  }): Promise<AccessTokenData> {
    try {
      const token = this.generateSecureToken();
      
      // Verificar se token já existe
      const existingUser = await postgresRepository.findUserByAccessToken(token);
      if (existingUser) {
        throw new Error('Token já existe no sistema');
      }

      // Criar usuário do sistema externo no PostgreSQL
      const userData = {
        nome: options.name,
        cpf: this.generateFakeCPF(),
        email: options.email,
        access_token: token,
        description: `Usuário do sistema prefeitura - ${options.secretaria}`,
        conta_ativa: true,
        is_anonymous: false,
        external_user_id: options.externalUserId,
        secretaria: options.secretaria,
        cargo: options.cargo || '',
        first_access_date: new Date(),
        created_at: new Date()
      };

      const user = await postgresRepository.createExternalUser(userData);

      return {
        token,
        userId: user.id,
        description: userData.description,
        createdAt: user.created_at,
        isActive: true
      };

    } catch (error: any) {
      console.error('Erro ao criar token para usuário externo:', error);
      throw new Error(`Falha ao criar token: ${error.message}`);
    }
  }

  /**
   * Verificar integridade do token
   */
  async verifyTokenIntegrity(token: string): Promise<{
    valid: boolean;
    user?: any;
    error?: string;
  }> {
    try {
      const user = await this.validateAccessToken(token);
      
      if (!user) {
        return {
          valid: false,
          error: 'Token inválido ou expirado'
        };
      }

      return {
        valid: true,
        user
      };

    } catch (error: any) {
      return {
        valid: false,
        error: error.message
      };
    }
  }
}

// Singleton instance
export const accessTokenService = new AccessTokenService();