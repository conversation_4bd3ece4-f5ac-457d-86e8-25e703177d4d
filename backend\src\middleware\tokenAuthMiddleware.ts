/**
 * Middleware para autenticação automática por token de acesso
 * Suporta tanto JWT tradicional quanto tokens de acesso permanentes
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { accessTokenService } from '../services/accessTokenService';

interface AuthenticatedRequest extends Request {
  user?: any;
}

/**
 * Middleware flexível que tenta várias formas de autenticação
 */
export const flexibleAuth = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    let token: string | null = null;
    let authType: 'jwt' | 'access_token' | null = null;

    // 1. Tentar Authorization header (JWT tradicional)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
      authType = 'jwt';
    }

    // 2. Tentar query parameter ?token= (access tokens)
    if (!token && req.query.token) {
      token = req.query.token as string;
      authType = 'access_token';
    }

    // 3. Tentar header X-Access-Token
    if (!token && req.headers['x-access-token']) {
      token = req.headers['x-access-token'] as string;
      authType = 'access_token';
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Token de acesso não fornecido'
      });
    }

    let user = null;

    // Processar baseado no tipo de autenticação
    if (authType === 'jwt') {
      // Validação JWT tradicional
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key-here') as any;
        user = decoded;
      } catch (jwtError) {
        // Se JWT falhar, tentar como access token
        user = await accessTokenService.validateAccessToken(token);
      }
    } else if (authType === 'access_token') {
      // Validação de access token
      user = await accessTokenService.validateAccessToken(token);
    }

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Token inválido ou expirado'
      });
    }

    // Adicionar informações do usuário à requisição
    req.user = user;
    next();

  } catch (error) {
    console.error('Erro na autenticação flexível:', error);
    return res.status(500).json({
      success: false,
      error: 'Erro interno na autenticação'
    });
  }
};

/**
 * Middleware específico para access tokens (sem JWT)
 */
export const accessTokenAuth = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    let token: string | null = null;

    // Buscar token em diferentes locais
    if (req.query.token) {
      token = req.query.token as string;
    } else if (req.headers['x-access-token']) {
      token = req.headers['x-access-token'] as string;
    } else if (req.body.token) {
      token = req.body.token;
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Token de acesso obrigatório'
      });
    }

    const user = await accessTokenService.validateAccessToken(token);

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Token de acesso inválido'
      });
    }

    req.user = user;
    next();

  } catch (error) {
    console.error('Erro na autenticação por access token:', error);
    return res.status(500).json({
      success: false,
      error: 'Erro interno na autenticação'
    });
  }
};

/**
 * Middleware que permite tanto autenticação quanto acesso anônimo
 */
export const optionalAuth = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    // Tentar autenticação flexível
    await flexibleAuth(req, res, (err) => {
      if (err) {
        // Se houver erro na autenticação, continuar sem usuário
        req.user = null;
      }
      next();
    });
  } catch (error) {
    // Em caso de erro, continuar sem autenticação
    req.user = null;
    next();
  }
};